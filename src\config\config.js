/**
 * 应用配置管理模块
 * 负责加载和管理所有环境变量和应用配置
 */

require('dotenv').config();

/**
 * 根据NODE_ENV智能生成日志配置
 * 提供合理的默认值，减少配置复杂度
 */
function getLogConfigByEnv(nodeEnv) {
    switch(nodeEnv) {
        case 'production':
            return {
                level: 'warn',
                fileEnabled: true,
                consoleEnabled: false,
                maxSize: '50m',
                maxFiles: '30d',
                datePattern: 'YYYY-MM-DD',
                timeFormat: 'YYYY-MM-DD HH:mm:ss'
            };
        case 'development':
            return {
                level: 'debug',
                fileEnabled: true,
                consoleEnabled: true,
                maxSize: '20m',
                maxFiles: '7d',
                datePattern: 'YYYY-MM-DD',
                timeFormat: 'YYYY-MM-DD HH:mm:ss'
            };
        case 'test':
            return {
                level: 'error',
                fileEnabled: false,
                consoleEnabled: false,
                maxSize: '10m',
                maxFiles: '3d',
                datePattern: 'YYYY-MM-DD',
                timeFormat: 'YYYY-MM-DD HH:mm:ss'
            };
        default:
            return {
                level: 'info',
                fileEnabled: true,
                consoleEnabled: true,
                maxSize: '20m',
                maxFiles: '14d',
                datePattern: 'YYYY-MM-DD',
                timeFormat: 'YYYY-MM-DD HH:mm:ss'
            };
    }
}

// 获取当前环境
const nodeEnv = process.env.NODE_ENV || 'development';

// 获取智能日志配置
const smartLogConfig = getLogConfigByEnv(nodeEnv);

const config = {
    // 服务器基础配置
    server: {
        port: parseInt(process.env.PORT) || 50090,
        host: process.env.HOST || 'localhost',
        env: nodeEnv
    },

    // 性能和限制配置
    performance: {
        timeout: parseInt(process.env.TIMEOUT) || 30000,
        batchConcurrency: parseInt(process.env.BATCH_CONCURRENCY) || 5
        // maxBatchSize 已移除 - 无实际功能实现
    },

    // 智能日志配置 - 基于NODE_ENV自动设置
    logging: {
        level: smartLogConfig.level,
        fileEnabled: smartLogConfig.fileEnabled,
        consoleEnabled: smartLogConfig.consoleEnabled,
        maxFiles: smartLogConfig.maxFiles,
        maxSize: smartLogConfig.maxSize,
        datePattern: smartLogConfig.datePattern,
        timeFormat: smartLogConfig.timeFormat
    },

    // UnblockNeteaseMusic 配置
    music: {
        // 音源优先级配置
        sources: process.env.MUSIC_SOURCES?.split(',').map(s => s.trim()) || ['qq', 'kugou', 'kuwo', 'migu'],

        // 第三方音源配置 (只有Cookie相关配置需要传递)
        neteaseCookie: process.env.NETEASE_COOKIE || '',
        qqCookie: process.env.QQ_COOKIE || '',
        miguCookie: process.env.MIGU_COOKIE || '',
        jooxCookie: process.env.JOOX_COOKIE || '',
        youtubeKey: process.env.YOUTUBE_KEY || '',

        // 功能开关 (只在用户明确配置时才设置，否则使用库默认值)
        enableFlac: process.env.ENABLE_FLAC,
        enableLocalVip: process.env.ENABLE_LOCAL_VIP,
        followSourceOrder: process.env.FOLLOW_SOURCE_ORDER,
        blockAds: process.env.BLOCK_ADS,
        selectMaxBr: process.env.SELECT_MAX_BR
    },

    // 安全配置
    security: {
        rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15分钟
        rateLimitMaxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100
    }
};

/**
 * 验证必要的配置项
 */
function validateConfig() {
    const errors = [];

    // 验证端口号
    if (isNaN(config.server.port) || config.server.port < 1 || config.server.port > 65535) {
        errors.push('无效的端口号配置');
    }

    // 验证性能配置
    if (config.performance.timeout < 5000 || config.performance.timeout > 120000) {
        errors.push('超时配置应在5-120秒之间');
    }

    // 验证性能配置
    if (config.performance.batchConcurrency < 1 || config.performance.batchConcurrency > 20) {
        errors.push('批量并发数应在1-20之间');
    }

    // 验证音源配置
    const validSources = ['qq', 'kugou', 'kuwo', 'migu', 'joox', 'youtube', 'ytdlp', 'bilibili'];
    const invalidSources = config.music.sources.filter(source => !validSources.includes(source));
    if (invalidSources.length > 0) {
        errors.push(`无效的音源配置: ${invalidSources.join(', ')}`);
    }

    if (errors.length > 0) {
        throw new Error(`配置验证失败:\n${errors.join('\n')}`);
    }
}

// 验证配置
validateConfig();

module.exports = config;
