#!/usr/bin/env node

/**
 * UnblockNeteaseMusic Backend - 性能测试运行器
 * Performance Test Runner for Music Unlock Service
 */

const axios = require('axios');
const { performance } = require('perf_hooks');
const fs = require('fs');
const path = require('path');

// 测试配置
const PERFORMANCE_CONFIG = {
    server: {
        baseUrl: 'http://localhost:50091',
        timeout: 30000
    },
    tests: {
        warmup: 5,           // 预热请求数
        concurrent: 50,      // 并发请求数
        duration: 60,        // 测试持续时间(秒)
        intervals: 10        // 统计间隔(秒)
    },
    thresholds: {
        responseTime: 3000,  // 响应时间阈值(ms)
        successRate: 95,     // 成功率阈值(%)
        memoryLimit: 200,    // 内存限制(MB)
        cpuLimit: 80         // CPU使用率限制(%)
    },
    endpoints: [
        { path: '/health', method: 'GET', name: '健康检查' },
        { path: '/music/source', method: 'GET', name: '音源管理' },
        { path: '/', method: 'GET', name: '首页API文档' }
    ]
};

// 颜色定义
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

// 日志函数
function log(level, message) {
    const timestamp = new Date().toISOString();
    const color = colors[level] || colors.reset;
    console.log(`${color}[${level.toUpperCase()}]${colors.reset} ${timestamp} - ${message}`);
}

function logHeader(title) {
    console.log('\n' + colors.cyan + '='.repeat(60));
    console.log(`⚡ ${title}`);
    console.log('='.repeat(60) + colors.reset);
}

function logSuccess(message) {
    log('success', `✅ ${message}`);
}

function logError(message) {
    log('error', `❌ ${message}`);
}

function logInfo(message) {
    log('info', `ℹ️ ${message}`);
}

function logWarning(message) {
    log('warning', `⚠️ ${message}`);
}

// 性能测试类
class PerformanceTester {
    constructor() {
        this.results = [];
        this.errors = [];
        this.startTime = null;
        this.endTime = null;
    }

    // 单个请求测试
    async testSingleRequest(endpoint) {
        const startTime = performance.now();
        const startMemory = process.memoryUsage();
        
        try {
            const response = await axios({
                method: endpoint.method,
                url: `${PERFORMANCE_CONFIG.server.baseUrl}${endpoint.path}`,
                timeout: PERFORMANCE_CONFIG.server.timeout,
                validateStatus: () => true // 接受所有状态码
            });
            
            const endTime = performance.now();
            const endMemory = process.memoryUsage();
            
            const result = {
                endpoint: endpoint.name,
                path: endpoint.path,
                method: endpoint.method,
                responseTime: endTime - startTime,
                statusCode: response.status,
                success: response.status >= 200 && response.status < 400,
                memoryUsed: endMemory.heapUsed - startMemory.heapUsed,
                timestamp: new Date().toISOString()
            };
            
            this.results.push(result);
            return result;
            
        } catch (error) {
            const endTime = performance.now();
            
            const errorResult = {
                endpoint: endpoint.name,
                path: endpoint.path,
                method: endpoint.method,
                responseTime: endTime - startTime,
                statusCode: 0,
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
            
            this.errors.push(errorResult);
            return errorResult;
        }
    }

    // 并发测试
    async testConcurrent(endpoint, concurrency = 10, duration = 30) {
        logInfo(`开始并发测试: ${endpoint.name} (${concurrency}并发, ${duration}秒)`);
        
        const startTime = Date.now();
        const endTime = startTime + (duration * 1000);
        const promises = [];
        
        // 启动并发请求
        for (let i = 0; i < concurrency; i++) {
            promises.push(this.runContinuousRequests(endpoint, endTime));
        }
        
        // 等待所有请求完成
        await Promise.all(promises);
        
        logSuccess(`并发测试完成: ${endpoint.name}`);
    }

    // 持续请求
    async runContinuousRequests(endpoint, endTime) {
        while (Date.now() < endTime) {
            await this.testSingleRequest(endpoint);
            // 短暂延迟避免过度压力
            await new Promise(resolve => setTimeout(resolve, 100));
        }
    }

    // 预热测试
    async warmup() {
        logInfo('开始预热测试...');
        
        for (const endpoint of PERFORMANCE_CONFIG.endpoints) {
            for (let i = 0; i < PERFORMANCE_CONFIG.tests.warmup; i++) {
                await this.testSingleRequest(endpoint);
            }
        }
        
        logSuccess('预热测试完成');
    }

    // 分析结果
    analyzeResults() {
        logHeader('性能测试结果分析');
        
        if (this.results.length === 0) {
            logWarning('没有测试结果可分析');
            return null;
        }
        
        const analysis = {};
        
        // 按端点分组分析
        for (const endpoint of PERFORMANCE_CONFIG.endpoints) {
            const endpointResults = this.results.filter(r => r.endpoint === endpoint.name);
            
            if (endpointResults.length === 0) continue;
            
            const responseTimes = endpointResults.map(r => r.responseTime);
            const successCount = endpointResults.filter(r => r.success).length;
            const successRate = (successCount / endpointResults.length) * 100;
            
            analysis[endpoint.name] = {
                totalRequests: endpointResults.length,
                successCount: successCount,
                failureCount: endpointResults.length - successCount,
                successRate: successRate,
                avgResponseTime: responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length,
                minResponseTime: Math.min(...responseTimes),
                maxResponseTime: Math.max(...responseTimes),
                p95ResponseTime: this.calculatePercentile(responseTimes, 95),
                p99ResponseTime: this.calculatePercentile(responseTimes, 99)
            };
            
            // 输出分析结果
            logInfo(`${endpoint.name} 性能分析:`);
            logInfo(`  总请求数: ${analysis[endpoint.name].totalRequests}`);
            logInfo(`  成功率: ${successRate.toFixed(2)}%`);
            logInfo(`  平均响应时间: ${analysis[endpoint.name].avgResponseTime.toFixed(2)}ms`);
            logInfo(`  P95响应时间: ${analysis[endpoint.name].p95ResponseTime.toFixed(2)}ms`);
            logInfo(`  P99响应时间: ${analysis[endpoint.name].p99ResponseTime.toFixed(2)}ms`);
            
            // 检查阈值
            if (successRate < PERFORMANCE_CONFIG.thresholds.successRate) {
                logWarning(`  ⚠️ 成功率低于阈值 ${PERFORMANCE_CONFIG.thresholds.successRate}%`);
            }
            
            if (analysis[endpoint.name].avgResponseTime > PERFORMANCE_CONFIG.thresholds.responseTime) {
                logWarning(`  ⚠️ 平均响应时间超过阈值 ${PERFORMANCE_CONFIG.thresholds.responseTime}ms`);
            }
        }
        
        return analysis;
    }

    // 计算百分位数
    calculatePercentile(values, percentile) {
        const sorted = values.slice().sort((a, b) => a - b);
        const index = Math.ceil((percentile / 100) * sorted.length) - 1;
        return sorted[index] || 0;
    }

    // 生成性能报告
    generateReport(analysis) {
        const timestamp = new Date().toISOString();
        const reportPath = `test-reports/performance-report-${timestamp.replace(/[:.]/g, '-')}.md`;
        
        // 确保报告目录存在
        const reportDir = path.dirname(reportPath);
        if (!fs.existsSync(reportDir)) {
            fs.mkdirSync(reportDir, { recursive: true });
        }
        
        let report = `# ⚡ 性能测试报告

## 📊 测试执行摘要
- **执行时间**: ${new Date().toLocaleString()}
- **测试环境**: Node.js ${process.version}
- **服务器地址**: ${PERFORMANCE_CONFIG.server.baseUrl}
- **并发数**: ${PERFORMANCE_CONFIG.tests.concurrent}
- **测试持续时间**: ${PERFORMANCE_CONFIG.tests.duration}秒

## 🎯 性能阈值
- **响应时间**: < ${PERFORMANCE_CONFIG.thresholds.responseTime}ms
- **成功率**: > ${PERFORMANCE_CONFIG.thresholds.successRate}%
- **内存限制**: < ${PERFORMANCE_CONFIG.thresholds.memoryLimit}MB
- **CPU限制**: < ${PERFORMANCE_CONFIG.thresholds.cpuLimit}%

## 📈 测试结果

`;

        if (analysis) {
            for (const [endpointName, stats] of Object.entries(analysis)) {
                const successRateStatus = stats.successRate >= PERFORMANCE_CONFIG.thresholds.successRate ? '✅' : '❌';
                const responseTimeStatus = stats.avgResponseTime <= PERFORMANCE_CONFIG.thresholds.responseTime ? '✅' : '❌';
                
                report += `### ${endpointName}

| 指标 | 值 | 状态 |
|------|----|----- |
| 总请求数 | ${stats.totalRequests} | - |
| 成功率 | ${stats.successRate.toFixed(2)}% | ${successRateStatus} |
| 平均响应时间 | ${stats.avgResponseTime.toFixed(2)}ms | ${responseTimeStatus} |
| 最小响应时间 | ${stats.minResponseTime.toFixed(2)}ms | - |
| 最大响应时间 | ${stats.maxResponseTime.toFixed(2)}ms | - |
| P95响应时间 | ${stats.p95ResponseTime.toFixed(2)}ms | - |
| P99响应时间 | ${stats.p99ResponseTime.toFixed(2)}ms | - |

`;
            }
        }

        report += `## 🔍 错误统计
- **总错误数**: ${this.errors.length}
- **错误率**: ${this.results.length > 0 ? (this.errors.length / (this.results.length + this.errors.length) * 100).toFixed(2) : 0}%

## 📝 建议
${this.generateRecommendations(analysis)}

---
*报告生成时间: ${timestamp}*
`;
        
        fs.writeFileSync(reportPath, report);
        logSuccess(`性能测试报告已生成: ${reportPath}`);
        
        return reportPath;
    }

    // 生成建议
    generateRecommendations(analysis) {
        const recommendations = [];
        
        if (!analysis) {
            return '❌ 无法生成建议，测试结果不足。';
        }
        
        for (const [endpointName, stats] of Object.entries(analysis)) {
            if (stats.successRate < PERFORMANCE_CONFIG.thresholds.successRate) {
                recommendations.push(`⚠️ ${endpointName} 成功率偏低，建议检查服务稳定性`);
            }
            
            if (stats.avgResponseTime > PERFORMANCE_CONFIG.thresholds.responseTime) {
                recommendations.push(`⚠️ ${endpointName} 响应时间偏高，建议优化性能`);
            }
            
            if (stats.p99ResponseTime > stats.avgResponseTime * 3) {
                recommendations.push(`⚠️ ${endpointName} P99响应时间波动较大，建议检查异常情况`);
            }
        }
        
        if (recommendations.length === 0) {
            return '✅ 所有性能指标正常，系统表现良好。';
        }
        
        return recommendations.join('\n');
    }
}

// 检查服务器状态
async function checkServerStatus() {
    logInfo('检查服务器状态...');
    
    try {
        const response = await axios.get(`${PERFORMANCE_CONFIG.server.baseUrl}/health`, {
            timeout: 5000
        });
        
        if (response.status === 200) {
            logSuccess('服务器运行正常');
            return true;
        } else {
            logError(`服务器状态异常: ${response.status}`);
            return false;
        }
    } catch (error) {
        logError(`无法连接到服务器: ${error.message}`);
        logInfo('请确保服务器已启动: npm start');
        return false;
    }
}

// 主函数
async function main() {
    try {
        logHeader('UnblockNeteaseMusic Backend - 性能测试运行器');
        
        // 检查服务器状态
        const serverReady = await checkServerStatus();
        if (!serverReady) {
            process.exit(1);
        }
        
        // 创建性能测试器
        const tester = new PerformanceTester();
        
        // 预热
        await tester.warmup();
        
        // 运行性能测试
        logHeader('开始性能测试');
        
        for (const endpoint of PERFORMANCE_CONFIG.endpoints) {
            await tester.testConcurrent(
                endpoint,
                PERFORMANCE_CONFIG.tests.concurrent,
                PERFORMANCE_CONFIG.tests.duration
            );
        }
        
        // 分析结果
        const analysis = tester.analyzeResults();
        
        // 生成报告
        const reportPath = tester.generateReport(analysis);
        
        // 显示摘要
        logHeader('性能测试完成');
        logInfo(`测试报告: ${reportPath}`);
        logInfo(`总请求数: ${tester.results.length}`);
        logInfo(`错误数: ${tester.errors.length}`);
        
        // 判断测试是否通过
        let allPassed = true;
        if (analysis) {
            for (const stats of Object.values(analysis)) {
                if (stats.successRate < PERFORMANCE_CONFIG.thresholds.successRate ||
                    stats.avgResponseTime > PERFORMANCE_CONFIG.thresholds.responseTime) {
                    allPassed = false;
                    break;
                }
            }
        }
        
        if (allPassed) {
            logSuccess('🎉 所有性能测试通过！');
            process.exit(0);
        } else {
            logWarning('⚠️ 部分性能测试未达标，请查看报告');
            process.exit(1);
        }
        
    } catch (error) {
        logError(`性能测试运行器错误: ${error.message}`);
        process.exit(1);
    }
}

// 运行主函数
if (require.main === module) {
    main();
}

module.exports = { PerformanceTester, checkServerStatus };
