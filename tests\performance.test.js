/**
 * 音乐解锁服务性能测试脚本
 * 测试API性能和并发处理能力
 * 注意：此文件不包含Jest测试，仅为性能测试工具
 */

// 跳过Jest测试
describe.skip('性能测试工具', () => {
    test('此文件不包含实际测试', () => {
        expect(true).toBe(true);
    });
});

const axios = require('axios');
const { performance } = require('perf_hooks');

// 测试配置
const BASE_URL = 'http://localhost:3000';
const API_BASE = `${BASE_URL}/music`;
const TEST_SONG_ID = '418602084';
const CONCURRENT_REQUESTS = 50;
const PERFORMANCE_THRESHOLD = {
    RESPONSE_TIME: 2000, // 2秒
    SUCCESS_RATE: 0.95,  // 95%成功率
    MEMORY_LIMIT: 200    // 200MB内存限制
};

/**
 * 性能测试工具类
 */
class PerformanceTester {
    constructor() {
        this.results = [];
        this.errors = [];
    }

    /**
     * 单个请求性能测试
     */
    async testSingleRequest(url, method = 'GET', data = null) {
        const startTime = performance.now();
        const startMemory = process.memoryUsage();

        try {
            const config = {
                method,
                url,
                timeout: 10000,
                ...(data && { data })
            };

            const response = await axios(config);
            const endTime = performance.now();
            const endMemory = process.memoryUsage();

            const result = {
                url,
                method,
                status: response.status,
                responseTime: endTime - startTime,
                memoryUsed: (endMemory.heapUsed - startMemory.heapUsed) / 1024 / 1024, // MB
                success: response.status >= 200 && response.status < 300,
                timestamp: new Date().toISOString()
            };

            this.results.push(result);
            return result;
        } catch (error) {
            const endTime = performance.now();
            const errorResult = {
                url,
                method,
                status: error.response?.status || 0,
                responseTime: endTime - startTime,
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };

            this.errors.push(errorResult);
            return errorResult;
        }
    }

    /**
     * 并发请求测试
     */
    async testConcurrentRequests(url, concurrency = CONCURRENT_REQUESTS, method = 'GET', data = null) {
        console.log(`\n🚀 开始并发测试: ${concurrency} 个并发请求到 ${url}`);
        
        const startTime = performance.now();
        const promises = Array(concurrency).fill().map(() => 
            this.testSingleRequest(url, method, data)
        );

        const results = await Promise.all(promises);
        const endTime = performance.now();

        const successCount = results.filter(r => r.success).length;
        const avgResponseTime = results.reduce((sum, r) => sum + r.responseTime, 0) / results.length;
        const maxResponseTime = Math.max(...results.map(r => r.responseTime));
        const minResponseTime = Math.min(...results.map(r => r.responseTime));
        const successRate = successCount / results.length;

        const summary = {
            totalRequests: concurrency,
            successCount,
            failureCount: concurrency - successCount,
            successRate,
            totalTime: endTime - startTime,
            avgResponseTime,
            maxResponseTime,
            minResponseTime,
            requestsPerSecond: concurrency / ((endTime - startTime) / 1000)
        };

        console.log(`✅ 并发测试完成:`);
        console.log(`   - 总请求数: ${summary.totalRequests}`);
        console.log(`   - 成功数: ${summary.successCount}`);
        console.log(`   - 失败数: ${summary.failureCount}`);
        console.log(`   - 成功率: ${(summary.successRate * 100).toFixed(2)}%`);
        console.log(`   - 平均响应时间: ${summary.avgResponseTime.toFixed(2)}ms`);
        console.log(`   - 最大响应时间: ${summary.maxResponseTime.toFixed(2)}ms`);
        console.log(`   - 最小响应时间: ${summary.minResponseTime.toFixed(2)}ms`);
        console.log(`   - 每秒请求数: ${summary.requestsPerSecond.toFixed(2)} req/s`);

        return summary;
    }

    /**
     * 压力测试
     */
    async stressTest(url, duration = 30000, concurrency = 10) {
        console.log(`\n💪 开始压力测试: ${duration/1000}秒，并发数${concurrency}`);
        
        const startTime = Date.now();
        const endTime = startTime + duration;
        let requestCount = 0;
        let successCount = 0;

        const runRequests = async () => {
            while (Date.now() < endTime) {
                const promises = Array(concurrency).fill().map(async () => {
                    const result = await this.testSingleRequest(url);
                    requestCount++;
                    if (result.success) successCount++;
                    return result;
                });

                await Promise.all(promises);
                await new Promise(resolve => setTimeout(resolve, 100)); // 100ms间隔
            }
        };

        await runRequests();

        const actualDuration = Date.now() - startTime;
        const summary = {
            duration: actualDuration,
            totalRequests: requestCount,
            successCount,
            failureCount: requestCount - successCount,
            successRate: successCount / requestCount,
            requestsPerSecond: requestCount / (actualDuration / 1000)
        };

        console.log(`✅ 压力测试完成:`);
        console.log(`   - 测试时长: ${(summary.duration/1000).toFixed(2)}秒`);
        console.log(`   - 总请求数: ${summary.totalRequests}`);
        console.log(`   - 成功数: ${summary.successCount}`);
        console.log(`   - 成功率: ${(summary.successRate * 100).toFixed(2)}%`);
        console.log(`   - 每秒请求数: ${summary.requestsPerSecond.toFixed(2)} req/s`);

        return summary;
    }

    /**
     * 内存泄漏测试
     */
    async memoryLeakTest(url, iterations = 1000) {
        console.log(`\n🧠 开始内存泄漏测试: ${iterations} 次迭代`);
        
        const initialMemory = process.memoryUsage();
        const memorySnapshots = [];

        for (let i = 0; i < iterations; i++) {
            await this.testSingleRequest(url);
            
            if (i % 100 === 0) {
                const currentMemory = process.memoryUsage();
                memorySnapshots.push({
                    iteration: i,
                    heapUsed: currentMemory.heapUsed / 1024 / 1024,
                    heapTotal: currentMemory.heapTotal / 1024 / 1024,
                    external: currentMemory.external / 1024 / 1024
                });
                
                // 强制垃圾回收（如果可用）
                if (global.gc) {
                    global.gc();
                }
            }
        }

        const finalMemory = process.memoryUsage();
        const memoryIncrease = (finalMemory.heapUsed - initialMemory.heapUsed) / 1024 / 1024;

        console.log(`✅ 内存泄漏测试完成:`);
        console.log(`   - 初始内存: ${(initialMemory.heapUsed / 1024 / 1024).toFixed(2)}MB`);
        console.log(`   - 最终内存: ${(finalMemory.heapUsed / 1024 / 1024).toFixed(2)}MB`);
        console.log(`   - 内存增长: ${memoryIncrease.toFixed(2)}MB`);
        console.log(`   - 内存增长率: ${((memoryIncrease / (initialMemory.heapUsed / 1024 / 1024)) * 100).toFixed(2)}%`);

        return {
            initialMemory: initialMemory.heapUsed / 1024 / 1024,
            finalMemory: finalMemory.heapUsed / 1024 / 1024,
            memoryIncrease,
            snapshots: memorySnapshots
        };
    }

    /**
     * 生成性能报告
     */
    generateReport() {
        const successfulResults = this.results.filter(r => r.success);
        const failedResults = this.results.filter(r => !r.success);

        if (successfulResults.length === 0) {
            return {
                totalRequests: this.results.length,
                successRate: 0,
                avgResponseTime: 0,
                report: '所有请求都失败了'
            };
        }

        const avgResponseTime = successfulResults.reduce((sum, r) => sum + r.responseTime, 0) / successfulResults.length;
        const maxResponseTime = Math.max(...successfulResults.map(r => r.responseTime));
        const minResponseTime = Math.min(...successfulResults.map(r => r.responseTime));
        const successRate = successfulResults.length / this.results.length;

        const report = {
            totalRequests: this.results.length,
            successfulRequests: successfulResults.length,
            failedRequests: failedResults.length,
            successRate,
            avgResponseTime,
            maxResponseTime,
            minResponseTime,
            performanceGrade: this.getPerformanceGrade(avgResponseTime, successRate)
        };

        return report;
    }

    /**
     * 获取性能等级
     */
    getPerformanceGrade(avgResponseTime, successRate) {
        if (successRate < 0.9) return 'F - 不可接受';
        if (avgResponseTime > 3000) return 'D - 较差';
        if (avgResponseTime > 2000) return 'C - 一般';
        if (avgResponseTime > 1000) return 'B - 良好';
        return 'A - 优秀';
    }
}

/**
 * 主测试函数
 */
async function runPerformanceTests() {
    console.log('🎵 音乐解锁服务性能测试开始\n');
    
    const tester = new PerformanceTester();

    try {
        // 1. 健康检查
        console.log('1️⃣ 健康检查测试');
        await tester.testConcurrentRequests(`${BASE_URL}/health`, 20);

        // 2. API信息测试
        console.log('\n2️⃣ API信息测试');
        await tester.testConcurrentRequests(`${API_BASE}`, 20);

        // 3. 歌曲信息API测试
        console.log('\n3️⃣ 歌曲信息API测试');
        await tester.testConcurrentRequests(`${API_BASE}/song/${TEST_SONG_ID}`, 30);

        // 4. 搜索API测试
        console.log('\n4️⃣ 搜索API测试');
        await tester.testConcurrentRequests(`${API_BASE}/search/keyword?q=周杰伦&page=1&pageSize=10`, 25);

        // 5. 解锁API测试
        console.log('\n5️⃣ 解锁API测试');
        await tester.testConcurrentRequests(
            `${API_BASE}/unlock`, 
            20, 
            'POST', 
            { songIds: [parseInt(TEST_SONG_ID)], minBitrate: 128000 }
        );

        // 6. 音源管理API测试
        console.log('\n6️⃣ 音源管理API测试');
        await tester.testConcurrentRequests(`${API_BASE}/sources`, 25);

        // 7. 压力测试
        console.log('\n7️⃣ 压力测试');
        await tester.stressTest(`${API_BASE}/song/${TEST_SONG_ID}`, 15000, 5);

        // 8. 内存泄漏测试
        console.log('\n8️⃣ 内存泄漏测试');
        await tester.memoryLeakTest(`${BASE_URL}/health`, 500);

        // 生成最终报告
        console.log('\n📊 性能测试报告');
        const report = tester.generateReport();
        console.log('='.repeat(50));
        console.log(`总请求数: ${report.totalRequests}`);
        console.log(`成功请求数: ${report.successfulRequests}`);
        console.log(`失败请求数: ${report.failedRequests}`);
        console.log(`成功率: ${(report.successRate * 100).toFixed(2)}%`);
        console.log(`平均响应时间: ${report.avgResponseTime.toFixed(2)}ms`);
        console.log(`最大响应时间: ${report.maxResponseTime.toFixed(2)}ms`);
        console.log(`最小响应时间: ${report.minResponseTime.toFixed(2)}ms`);
        console.log(`性能等级: ${report.performanceGrade}`);
        console.log('='.repeat(50));

        // 性能评估
        const passed = report.successRate >= PERFORMANCE_THRESHOLD.SUCCESS_RATE && 
                      report.avgResponseTime <= PERFORMANCE_THRESHOLD.RESPONSE_TIME;

        if (passed) {
            console.log('✅ 性能测试通过！');
        } else {
            console.log('❌ 性能测试未通过，需要优化。');
        }

        return report;

    } catch (error) {
        console.error('❌ 性能测试过程中发生错误:', error.message);
        throw error;
    }
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
    runPerformanceTests()
        .then(() => {
            console.log('\n🎉 性能测试完成！');
            process.exit(0);
        })
        .catch((error) => {
            console.error('\n💥 性能测试失败:', error.message);
            process.exit(1);
        });
}

module.exports = { PerformanceTester, runPerformanceTests };
