/**
 * 音乐解锁服务主应用入口
 * 基于Express.js和UnblockNeteaseMusic的音乐解锁服务后端
 */

const express = require('express');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');

// 导入配置和工具
const config = require('./config/config');
const { logger, requestLogger } = require('./middleware/logger');
const { errorHandler, notFoundHandler, setupProcessErrorHandlers } = require('./middleware/errorHandler');

// 设置进程级错误处理
setupProcessErrorHandlers();

// 创建Express应用
const app = express();

// 信任代理（用于获取真实IP）
app.set('trust proxy', 1);

// 安全中间件
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ['\'self\''],
            styleSrc: ['\'self\'', '\'unsafe-inline\''], // 保留CSS内联样式支持
            scriptSrc: ['\'self\''], // 移除unsafe-inline，提高安全性
            imgSrc: ['\'self\'', 'data:', 'https:'],
            connectSrc: ['\'self\''], // 允许AJAX请求
            fontSrc: ['\'self\''], // 允许字体加载
        },
    },
}));

// 请求体解析中间件 - 使用Express默认限制(100kb)
app.use(express.json()); // 默认100kb限制，适合音乐解锁服务
app.use(express.urlencoded({ extended: true })); // 默认100kb限制

// 请求超时中间件
app.use((req, _res, next) => {
    req.setTimeout(config.performance.timeout, () => {
        const error = new Error('请求超时');
        error.statusCode = 408;
        next(error);
    });
    next();
});

// 请求日志中间件
app.use(requestLogger);

// API限流中间件
const limiter = rateLimit({
    windowMs: config.security.rateLimitWindowMs,
    max: config.security.rateLimitMaxRequests,
    message: {
        code: 429,
        message: '请求过于频繁，请稍后再试',
        timestamp: new Date().toISOString()
    },
    standardHeaders: true,
    legacyHeaders: false,
});
app.use('/music', limiter);

// 根路径 - 完整的API文档
app.get('/', (req, res) => {
    const currentTime = new Date().toISOString();
    const version = require('../package.json').version;

    const baseUrl = `${req.protocol}://${req.get('host')}`;

    res.json({
        状态码: 200,
        消息: '音乐解锁服务API文档',
        时间戳: currentTime,
        版本: version,
        API文档: [
            {
                快速示例: {
                    单首歌曲解锁: `${baseUrl}/music/unlock?songs=418602084`,
                    批量歌曲解锁: `${baseUrl}/music/unlock?songs=418602084,123456,186016`,
                    获取音源信息: `${baseUrl}/music/source`
                }
            },
            {
                请求方法: 'GET',
                接口路径: '/music/unlock',
                接口描述: '音乐解锁服务,批量音乐解锁接口，支持多音源并行解锁',
                请求参数: {
                    sources: {
                        参数名称: '音源列表',
                        参数类型: '字符串',
                        是否必需: '否（可选）',
                        参数描述: '音源ID列表，逗号分隔。如不指定则使用环境变量MUSIC_SOURCES配置',
                        可选值: 'qq,migu,kuwo,kugou,joox,youtube',
                        示例值: 'qq,migu,kuwo',
                        配置优先级: 'API参数 > 环境变量 > 系统默认'
                    },
                    songs: {
                        参数名称: '歌曲列表',
                        参数类型: '字符串',
                        是否必需: '是',
                        参数描述: '歌曲ID列表，逗号分隔',
                        示例值: '418602084,123456'
                    }
                }
            },
            {
                请求方法: 'GET',
                接口路径: '/music/source',
                接口描述: '音源管理服务,获取整合的音源管理信息，包含音源列表、统计数据和配置信息',
                请求参数: {}
            }
        ]
    });
});

// 统一音乐服务路由
app.use('/music', require('./routes/musicRoutes'));

// 404错误处理
app.use(notFoundHandler);

// 全局错误处理中间件
app.use(errorHandler);

// 只在直接运行时启动服务器，测试时不启动
let server;
if (require.main === module) {
    // 启动服务器
    server = app.listen(config.server.port, config.server.host, () => {
        logger.info('🎵 音乐解锁服务启动成功', {
            host: config.server.host,
            port: config.server.port,
            environment: config.server.env,
            pid: process.pid
        });

        logger.info(`🔍 服务状态和API文档: http://${config.server.host}:${config.server.port}/`);
        logger.info(`🎯 API基础地址: http://${config.server.host}:${config.server.port}/music`);
        logger.info('📝 使用独立的HTML测试工具进行API测试');
    });

    // 优雅关闭处理
    process.on('SIGTERM', gracefulShutdown);
    process.on('SIGINT', gracefulShutdown);
}

function gracefulShutdown(signal) {
    logger.info(`收到${signal}信号，开始优雅关闭服务器...`);

    if (server) {
        server.close((err) => {
            if (err) {
                logger.error('服务器关闭时发生错误:', err);
                process.exit(1);
            }

            logger.info('服务器已优雅关闭');
            process.exit(0);
        });

        // 强制关闭超时
        setTimeout(() => {
            logger.error('强制关闭服务器');
            process.exit(1);
        }, 10000);
    }
}

module.exports = app;
