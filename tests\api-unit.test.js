/**
 * 单元测试脚本 - 不启动服务器
 * 直接测试Express应用实例
 */

const request = require('supertest');

// 设置测试超时时间
jest.setTimeout(30000);

// 测试配置
const TEST_SONG_ID = '418602084'; // 周杰伦-稻香
const API_BASE = '/music';

describe('音乐解锁服务API单元测试', () => {
    let app;
    
    beforeAll(async () => {
        // 设置测试环境变量
        process.env.NODE_ENV = 'test';
        process.env.PORT = '3001'; // 使用测试专用端口

        // 动态导入app以避免端口冲突
        app = require('../src/app');
    });
    
    // 根路径测试（替代健康检查）
    describe('根路径API文档', () => {
        test('GET / - 应该返回API文档', async () => {
            const response = await request(app)
                .get('/')
                .expect(200);

            expect(response.body.状态码).toBe(200);
            expect(response.body.消息).toBe('音乐解锁服务API文档');
            expect(response.body.版本).toBeDefined();
            expect(response.body.API文档).toBeDefined();
            expect(Array.isArray(response.body.API文档)).toBe(true);
        });
    });

    // 音源管理测试
    describe('音源管理', () => {
        test('GET /music/source - 应该返回音源信息', async () => {
            const response = await request(app)
                .get(`${API_BASE}/source`)
                .expect(200);

            expect(response.body.状态码).toBe(200);
            expect(response.body.音源总数).toBeDefined();
            expect(response.body.音源配置).toBeDefined();
            expect(Array.isArray(response.body.音源配置)).toBe(true);
        });

        test('GET /music/source - 应该返回音源统计', async () => {
            const response = await request(app)
                .get(`${API_BASE}/source`)
                .expect(200);

            expect(response.body.状态码).toBe(200);
            expect(response.body.已启用音源).toBeDefined();
            expect(response.body.已禁用音源).toBeDefined();
            expect(typeof response.body.音源总数).toBe('number');
        });
    });

    // 音乐解锁API测试
    describe('音乐解锁API', () => {
        test('GET /music/unlock?sources=migu&songs=:id - 应该返回解锁信息', async () => {
            const response = await request(app)
                .get(`${API_BASE}/unlock?sources=migu&songs=${TEST_SONG_ID}`)
                .expect(200);

            expect(response.body.状态码).toBe(200);
            expect(response.body.成功列表.歌曲ID).toBe(parseInt(TEST_SONG_ID));
            expect(response.body.解锁总数).toBe(1);
        });

        test('GET /music/unlock?sources=migu,kuwo&songs=:id - 应该支持多音源', async () => {
            const response = await request(app)
                .get(`${API_BASE}/unlock?sources=migu,kuwo&songs=${TEST_SONG_ID}`)
                .expect(200);

            expect(response.body.状态码).toBe(200);
            expect(response.body.成功列表.歌曲ID).toBe(parseInt(TEST_SONG_ID));
            expect(response.body.解锁总数).toBe(1);
        });

        test('GET /music/unlock - 批量解锁测试', async () => {
            const response = await request(app)
                .get(`${API_BASE}/unlock?sources=migu&songs=${TEST_SONG_ID},186016`)
                .expect(200);

            expect(response.body.状态码).toBe(200);
            expect(response.body.解锁总数).toBeGreaterThan(0);
            expect(response.body.解锁成功率).toBeDefined();
        });
    });

    // 参数验证测试
    describe('参数验证', () => {
        test('GET /music/unlock - sources参数可选，应该成功', async () => {
            const response = await request(app)
                .get(`${API_BASE}/unlock?songs=${TEST_SONG_ID}`)
                .expect(200);

            expect(response.body.状态码).toBe(200);
            expect(response.body.解锁总数).toBe(1);
        });

        test('GET /music/unlock - 缺少songs参数应返回400', async () => {
            const response = await request(app)
                .get(`${API_BASE}/unlock?sources=migu`)
                .expect(400);

            expect(response.body.状态码).toBe(400);
            expect(response.body.消息).toContain('songs');
            expect(response.body.错误代码).toBe('VALIDATION_ERROR');
        });

        test('GET /music/unlock - 无效歌曲ID应返回400', async () => {
            const response = await request(app)
                .get(`${API_BASE}/unlock?sources=migu&songs=invalid`)
                .expect(400);

            expect(response.body.状态码).toBe(400);
            expect(response.body.错误代码).toBe('VALIDATION_ERROR');
        });
    });

    // 错误处理测试
    describe('错误处理', () => {
        test('GET /music/nonexistent - 不存在的路径应返回404', async () => {
            await request(app)
                .get(`${API_BASE}/nonexistent`)
                .expect(404);
        });

        test('GET /api/nonexistent - 旧API路径应返回404', async () => {
            await request(app)
                .get('/api/unlock')
                .expect(404);
        });

        test('GET /health - 健康检查路径应返回404', async () => {
            await request(app)
                .get('/health')
                .expect(404);
        });
    });

    // Favicon和静态文件处理测试
    describe('Favicon和静态文件处理', () => {
        test('GET /favicon.ico - 应返回204 No Content而不是404错误', async () => {
            const response = await request(app)
                .get('/favicon.ico')
                .expect(204);

            // 确保没有返回JSON错误响应
            expect(response.body).toEqual({});
        });

        test('GET /robots.txt - 应返回204 No Content', async () => {
            await request(app)
                .get('/robots.txt')
                .expect(204);
        });

        test('GET /sitemap.xml - 应返回204 No Content', async () => {
            await request(app)
                .get('/sitemap.xml')
                .expect(204);
        });

        test('GET /apple-touch-icon.png - 应返回204 No Content', async () => {
            await request(app)
                .get('/apple-touch-icon.png')
                .expect(204);
        });

        test('真正的404路径仍应返回JSON错误响应', async () => {
            const response = await request(app)
                .get('/truly-nonexistent-path')
                .expect(404);

            // 确保返回JSON格式的错误响应
            expect(response.body.状态码).toBe(404);
            expect(response.body.消息).toContain('路由 GET /truly-nonexistent-path 未找到');
            expect(response.body.错误代码).toBe('NOT_FOUND');
        });
    });

    // 性能测试
    describe('性能测试', () => {
        test('API响应时间应在合理范围内', async () => {
            const start = Date.now();
            await request(app)
                .get('/')
                .expect(200);
            const duration = Date.now() - start;

            expect(duration).toBeLessThan(1000); // 应在1秒内响应
        });
    });
});
