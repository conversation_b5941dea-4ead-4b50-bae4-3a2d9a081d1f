#!/usr/bin/env node

/**
 * UnblockNeteaseMusic Backend - 构建验证器
 * Build Validator for Music Unlock Service
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// 验证配置
const VALIDATION_CONFIG = {
    requiredFiles: [
        'package.json',
        'src/app.js',
        'src/config/config.js',
        '.env',
        'README.md'
    ],
    requiredDirectories: [
        'src',
        'src/config',
        'src/controllers',
        'src/middleware',
        'src/routes',
        'src/services',
        'src/utils',
        'tests',
        'scripts',
        'logs'
    ],
    requiredScripts: [
        'start',
        'dev',
        'test',
        'lint',
        'build'
    ],
    healthCheckTimeout: 15000,
    testTimeout: 60000
};

// 颜色定义
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

// 日志函数
function log(level, message) {
    const timestamp = new Date().toISOString();
    const color = colors[level] || colors.reset;
    console.log(`${color}[${level.toUpperCase()}]${colors.reset} ${timestamp} - ${message}`);
}

function logHeader(title) {
    console.log('\n' + colors.cyan + '='.repeat(60));
    console.log(`✅ ${title}`);
    console.log('='.repeat(60) + colors.reset);
}

function logSuccess(message) {
    log('success', `✅ ${message}`);
}

function logError(message) {
    log('error', `❌ ${message}`);
}

function logInfo(message) {
    log('info', `ℹ️ ${message}`);
}

function logWarning(message) {
    log('warning', `⚠️ ${message}`);
}

// 构建验证器
class BuildValidator {
    constructor() {
        this.validationResults = {
            fileStructure: false,
            packageConfiguration: false,
            codeQuality: false,
            functionality: false,
            performance: false
        };
        this.issues = [];
    }

    // 验证文件结构
    validateFileStructure() {
        logHeader('文件结构验证');
        
        let allValid = true;
        
        // 检查必需文件
        logInfo('检查必需文件...');
        for (const file of VALIDATION_CONFIG.requiredFiles) {
            if (fs.existsSync(file)) {
                logSuccess(`文件存在: ${file}`);
            } else {
                logError(`文件缺失: ${file}`);
                this.issues.push(`缺失文件: ${file}`);
                allValid = false;
            }
        }
        
        // 检查必需目录
        logInfo('检查必需目录...');
        for (const dir of VALIDATION_CONFIG.requiredDirectories) {
            if (fs.existsSync(dir) && fs.statSync(dir).isDirectory()) {
                logSuccess(`目录存在: ${dir}`);
            } else {
                logError(`目录缺失: ${dir}`);
                this.issues.push(`缺失目录: ${dir}`);
                allValid = false;
            }
        }
        
        // 检查关键配置文件内容
        this.validateConfigFiles();
        
        this.validationResults.fileStructure = allValid;
        return allValid;
    }

    // 验证配置文件
    validateConfigFiles() {
        logInfo('验证配置文件内容...');
        
        // 验证package.json
        try {
            const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
            
            // 检查必需脚本
            for (const script of VALIDATION_CONFIG.requiredScripts) {
                if (packageJson.scripts && packageJson.scripts[script]) {
                    logSuccess(`脚本存在: ${script}`);
                } else {
                    logWarning(`脚本缺失: ${script}`);
                    this.issues.push(`缺失npm脚本: ${script}`);
                }
            }
            
            // 检查关键字段
            const requiredFields = ['name', 'version', 'description', 'main'];
            for (const field of requiredFields) {
                if (packageJson[field]) {
                    logSuccess(`package.json字段存在: ${field}`);
                } else {
                    logWarning(`package.json字段缺失: ${field}`);
                }
            }
            
        } catch (error) {
            logError(`package.json解析失败: ${error.message}`);
            this.issues.push('package.json格式错误');
        }
        
        // 验证.env文件
        try {
            const envContent = fs.readFileSync('.env', 'utf8');
            const envLines = envContent.split('\n').filter(line => line.trim() && !line.startsWith('#'));
            
            if (envLines.length > 0) {
                logSuccess(`.env文件包含 ${envLines.length} 个配置项`);
            } else {
                logWarning('.env文件为空或只包含注释');
            }
            
        } catch (error) {
            logError(`.env文件读取失败: ${error.message}`);
            this.issues.push('.env文件不可读');
        }
    }

    // 验证包配置
    async validatePackageConfiguration() {
        logHeader('包配置验证');
        
        try {
            // 检查依赖完整性
            logInfo('检查依赖完整性...');
            await this.runCommand('npm ls --depth=0');
            logSuccess('依赖完整性验证通过');
            
            // 检查npm脚本
            logInfo('验证npm脚本...');
            const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
            
            for (const script of VALIDATION_CONFIG.requiredScripts) {
                if (packageJson.scripts && packageJson.scripts[script]) {
                    try {
                        // 测试脚本语法（不执行）
                        logSuccess(`脚本配置正确: ${script}`);
                    } catch (error) {
                        logWarning(`脚本配置可能有问题: ${script}`);
                    }
                }
            }
            
            this.validationResults.packageConfiguration = true;
            return true;
            
        } catch (error) {
            logError(`包配置验证失败: ${error.message}`);
            this.issues.push('包配置验证失败');
            return false;
        }
    }

    // 验证代码质量
    async validateCodeQuality() {
        logHeader('代码质量验证');
        
        try {
            // 运行ESLint检查
            logInfo('运行代码质量检查...');
            await this.runCommand('npm run lint');
            logSuccess('代码质量检查通过');
            
            // 检查代码结构
            this.validateCodeStructure();
            
            this.validationResults.codeQuality = true;
            return true;
            
        } catch (error) {
            logWarning('代码质量检查发现问题');
            this.issues.push('代码质量检查失败');
            
            // 尝试自动修复
            try {
                logInfo('尝试自动修复代码质量问题...');
                await this.runCommand('npm run lint:fix');
                logSuccess('代码质量问题已自动修复');
                this.validationResults.codeQuality = true;
                return true;
            } catch (fixError) {
                logError('自动修复失败，需要手动处理');
                return false;
            }
        }
    }

    // 验证代码结构
    validateCodeStructure() {
        logInfo('验证代码结构...');
        
        const srcFiles = this.getJavaScriptFiles('src');
        const testFiles = this.getJavaScriptFiles('tests');
        
        logInfo(`发现 ${srcFiles.length} 个源代码文件`);
        logInfo(`发现 ${testFiles.length} 个测试文件`);
        
        // 检查关键模块
        const keyModules = ['app.js', 'config/config.js'];
        for (const module of keyModules) {
            const modulePath = path.join('src', module);
            if (fs.existsSync(modulePath)) {
                logSuccess(`关键模块存在: ${module}`);
            } else {
                logError(`关键模块缺失: ${module}`);
                this.issues.push(`缺失关键模块: ${module}`);
            }
        }
    }

    // 验证功能性
    async validateFunctionality() {
        logHeader('功能性验证');
        
        try {
            // 测试应用启动
            logInfo('测试应用启动...');
            const startupTest = await this.testApplicationStartup();
            
            if (startupTest) {
                logSuccess('应用启动测试通过');
                
                // 运行基础测试
                logInfo('运行基础功能测试...');
                try {
                    await this.runCommand('npm run test:unit', VALIDATION_CONFIG.testTimeout);
                    logSuccess('基础功能测试通过');
                } catch (error) {
                    logWarning('基础功能测试失败，但不影响构建');
                }
                
                this.validationResults.functionality = true;
                return true;
            } else {
                logError('应用启动测试失败');
                this.issues.push('应用无法正常启动');
                return false;
            }
            
        } catch (error) {
            logError(`功能性验证失败: ${error.message}`);
            this.issues.push('功能性验证失败');
            return false;
        }
    }

    // 测试应用启动
    async testApplicationStartup() {
        return new Promise((resolve) => {
            const child = spawn('node', ['src/app.js'], {
                stdio: 'pipe',
                env: { ...process.env, NODE_ENV: 'test' }
            });
            
            let started = false;
            
            child.stdout.on('data', (data) => {
                const output = data.toString();
                if (output.includes('音乐解锁服务启动成功') || output.includes('Server running')) {
                    started = true;
                    child.kill();
                    resolve(true);
                }
            });
            
            child.stderr.on('data', (data) => {
                logWarning(`启动警告: ${data.toString()}`);
            });
            
            child.on('error', (error) => {
                logError(`启动错误: ${error.message}`);
                resolve(false);
            });
            
            // 超时处理
            setTimeout(() => {
                if (!started) {
                    child.kill();
                    resolve(false);
                }
            }, VALIDATION_CONFIG.healthCheckTimeout);
        });
    }

    // 验证性能
    async validatePerformance() {
        logHeader('性能验证');
        
        try {
            // 检查文件大小
            this.checkFileSizes();
            
            // 检查依赖大小
            await this.checkDependencySizes();
            
            this.validationResults.performance = true;
            return true;
            
        } catch (error) {
            logWarning(`性能验证失败: ${error.message}`);
            return false;
        }
    }

    // 检查文件大小
    checkFileSizes() {
        logInfo('检查文件大小...');
        
        const srcFiles = this.getJavaScriptFiles('src');
        let totalSize = 0;
        
        for (const file of srcFiles) {
            const stats = fs.statSync(file);
            totalSize += stats.size;
            
            if (stats.size > 100000) { // 100KB
                logWarning(`大文件: ${file} (${(stats.size / 1024).toFixed(2)}KB)`);
            }
        }
        
        logInfo(`源代码总大小: ${(totalSize / 1024).toFixed(2)}KB`);
    }

    // 检查依赖大小
    async checkDependencySizes() {
        try {
            const stats = fs.statSync('node_modules');
            const sizeInMB = (this.getDirectorySize('node_modules') / 1024 / 1024).toFixed(2);
            logInfo(`依赖包大小: ${sizeInMB}MB`);
            
            if (sizeInMB > 500) {
                logWarning('依赖包较大，可能影响部署速度');
            }
        } catch (error) {
            logWarning('无法检查依赖包大小');
        }
    }

    // 获取JavaScript文件列表
    getJavaScriptFiles(dir) {
        const files = [];
        
        function traverse(currentDir) {
            const items = fs.readdirSync(currentDir);
            
            for (const item of items) {
                const fullPath = path.join(currentDir, item);
                const stats = fs.statSync(fullPath);
                
                if (stats.isDirectory()) {
                    traverse(fullPath);
                } else if (item.endsWith('.js')) {
                    files.push(fullPath);
                }
            }
        }
        
        if (fs.existsSync(dir)) {
            traverse(dir);
        }
        
        return files;
    }

    // 获取目录大小
    getDirectorySize(dir) {
        let size = 0;
        
        function traverse(currentDir) {
            try {
                const items = fs.readdirSync(currentDir);
                
                for (const item of items) {
                    const fullPath = path.join(currentDir, item);
                    const stats = fs.statSync(fullPath);
                    
                    if (stats.isDirectory()) {
                        traverse(fullPath);
                    } else {
                        size += stats.size;
                    }
                }
            } catch (error) {
                // 忽略权限错误
            }
        }
        
        if (fs.existsSync(dir)) {
            traverse(dir);
        }
        
        return size;
    }

    // 运行命令
    runCommand(command, timeout = 30000) {
        return new Promise((resolve, reject) => {
            const child = spawn(command, {
                shell: true,
                stdio: 'pipe'
            });
            
            let stdout = '';
            let stderr = '';
            
            child.stdout.on('data', (data) => {
                stdout += data.toString();
            });
            
            child.stderr.on('data', (data) => {
                stderr += data.toString();
            });
            
            const timer = setTimeout(() => {
                child.kill();
                reject(new Error('Command timeout'));
            }, timeout);
            
            child.on('close', (code) => {
                clearTimeout(timer);
                if (code === 0) {
                    resolve(stdout.trim());
                } else {
                    reject(new Error(stderr || `Command failed with code ${code}`));
                }
            });
            
            child.on('error', (error) => {
                clearTimeout(timer);
                reject(error);
            });
        });
    }

    // 生成验证报告
    generateValidationReport() {
        logHeader('生成验证报告');
        
        const timestamp = new Date().toISOString();
        const reportPath = `build-validation-report-${timestamp.replace(/[:.]/g, '-')}.md`;
        
        const allPassed = Object.values(this.validationResults).every(result => result);
        const passedCount = Object.values(this.validationResults).filter(result => result).length;
        const totalCount = Object.keys(this.validationResults).length;
        
        const report = `# ✅ 构建验证报告

## 📊 验证摘要
- **验证时间**: ${new Date().toLocaleString()}
- **Node.js版本**: ${process.version}
- **项目版本**: v1.0.0
- **验证通过率**: ${((passedCount / totalCount) * 100).toFixed(2)}%

## 🎯 验证结果
- **文件结构**: ${this.validationResults.fileStructure ? '✅ 通过' : '❌ 失败'}
- **包配置**: ${this.validationResults.packageConfiguration ? '✅ 通过' : '❌ 失败'}
- **代码质量**: ${this.validationResults.codeQuality ? '✅ 通过' : '❌ 失败'}
- **功能性**: ${this.validationResults.functionality ? '✅ 通过' : '❌ 失败'}
- **性能**: ${this.validationResults.performance ? '✅ 通过' : '❌ 失败'}

## 📈 验证详情

### 文件结构验证
${this.validationResults.fileStructure ? '✅ 所有必需文件和目录都存在' : '❌ 部分文件或目录缺失'}

### 包配置验证
${this.validationResults.packageConfiguration ? '✅ 包配置正确，依赖完整' : '❌ 包配置存在问题'}

### 代码质量验证
${this.validationResults.codeQuality ? '✅ 代码质量符合标准' : '❌ 代码质量需要改进'}

### 功能性验证
${this.validationResults.functionality ? '✅ 应用功能正常' : '❌ 应用功能存在问题'}

### 性能验证
${this.validationResults.performance ? '✅ 性能指标正常' : '❌ 性能需要优化'}

## 🔍 发现的问题
${this.issues.length > 0 ? 
    this.issues.map(issue => `- ${issue}`).join('\n') : 
    '✅ 未发现问题'
}

## 📝 总体评估
${allPassed ? 
    '✅ **优秀** - 所有验证项目通过，项目构建质量优秀。' : 
    `⚠️ **需要改进** - ${totalCount - passedCount}个验证项目失败，需要修复问题。`
}

## 🚀 下一步建议
${allPassed ? 
    '- 项目已准备就绪，可以进行部署\n- 建议运行完整测试套件\n- 可以开始生产环境配置' :
    '- 修复发现的问题\n- 重新运行验证\n- 检查错误日志获取详细信息'
}

---
*报告生成时间: ${timestamp}*
`;
        
        fs.writeFileSync(reportPath, report);
        logSuccess(`构建验证报告已生成: ${reportPath}`);
        
        return reportPath;
    }

    // 运行完整验证
    async runFullValidation() {
        logHeader('UnblockNeteaseMusic Backend - 构建验证器');
        
        // 执行所有验证
        this.validateFileStructure();
        await this.validatePackageConfiguration();
        await this.validateCodeQuality();
        await this.validateFunctionality();
        await this.validatePerformance();
        
        // 生成报告
        const reportPath = this.generateValidationReport();
        
        // 显示摘要
        const allPassed = Object.values(this.validationResults).every(result => result);
        const passedCount = Object.values(this.validationResults).filter(result => result).length;
        const totalCount = Object.keys(this.validationResults).length;
        
        logHeader('构建验证完成');
        logInfo(`验证报告: ${reportPath}`);
        logInfo(`验证通过率: ${((passedCount / totalCount) * 100).toFixed(2)}%`);
        
        if (allPassed) {
            logSuccess('🎉 所有验证项目通过！');
            return true;
        } else {
            logWarning(`⚠️ ${totalCount - passedCount}个验证项目失败`);
            return false;
        }
    }
}

// 主函数
async function main() {
    try {
        const validator = new BuildValidator();
        const success = await validator.runFullValidation();
        
        process.exit(success ? 0 : 1);
        
    } catch (error) {
        logError(`构建验证器错误: ${error.message}`);
        process.exit(1);
    }
}

// 运行主函数
if (require.main === module) {
    main();
}

module.exports = { BuildValidator };
