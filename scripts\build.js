#!/usr/bin/env node

/**
 * 🔨 UnblockNeteaseMusic Backend - 构建脚本
 * Build Script for Production Deployment
 * 
 * 功能：
 * - 依赖安装和验证
 * - 环境配置检查
 * - 代码质量检查
 * - 生产环境优化
 * - 构建产物生成
 */

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');

class BuildManager {
    constructor() {
        this.buildResults = {
            dependencies: false,
            configuration: false,
            codeQuality: false,
            optimization: false,
            validation: false
        };
        this.startTime = Date.now();
        this.buildDir = 'dist';
        this.logFile = `logs/build-${new Date().toISOString().replace(/[:.]/g, '-')}.log`;
    }

    // 日志输出函数
    log(message, type = 'info') {
        const timestamp = new Date().toISOString();
        const prefix = {
            info: '📋',
            success: '✅',
            error: '❌',
            warning: '⚠️',
            build: '🔨'
        }[type] || '📋';
        
        const logMessage = `${prefix} [${timestamp}] ${message}`;
        console.log(logMessage);
        
        // 写入日志文件
        this.writeToLogFile(logMessage);
    }

    // 写入日志文件
    writeToLogFile(message) {
        try {
            const logDir = path.dirname(this.logFile);
            if (!fs.existsSync(logDir)) {
                fs.mkdirSync(logDir, { recursive: true });
            }
            fs.appendFileSync(this.logFile, message + '\n');
        } catch (error) {
            // 忽略日志写入错误
        }
    }

    // 执行命令并返回结果
    async runCommand(command, options = {}) {
        return new Promise((resolve, reject) => {
            exec(command, { cwd: process.cwd(), ...options }, (error, stdout, stderr) => {
                if (error) {
                    reject({ error, stdout, stderr });
                } else {
                    resolve({ stdout, stderr });
                }
            });
        });
    }

    // 1. 依赖管理
    async manageDependencies() {
        this.log('开始依赖管理...', 'build');
        
        try {
            // 清理node_modules（可选）
            if (process.argv.includes('--clean')) {
                this.log('清理现有依赖...', 'info');
                if (fs.existsSync('node_modules')) {
                    await this.runCommand('rm -rf node_modules');
                }
                if (fs.existsSync('package-lock.json')) {
                    fs.unlinkSync('package-lock.json');
                }
            }
            
            // 安装依赖
            this.log('安装项目依赖...', 'info');
            await this.runCommand('npm install --production=false');
            
            // 验证关键依赖
            const criticalDeps = [
                '@unblockneteasemusic/server',
                'express',
                'winston',
                'joi',
                'helmet',
                'dotenv'
            ];
            
            for (const dep of criticalDeps) {
                try {
                    require.resolve(dep);
                    this.log(`✓ ${dep}`, 'info');
                } catch (error) {
                    throw new Error(`关键依赖缺失: ${dep}`);
                }
            }
            
            // 安全审计
            try {
                await this.runCommand('npm audit --audit-level=high');
                this.log('依赖安全检查通过', 'success');
            } catch (error) {
                this.log('发现安全漏洞，建议运行 npm audit fix', 'warning');
            }
            
            this.buildResults.dependencies = true;
            this.log('依赖管理完成', 'success');
            return true;
            
        } catch (error) {
            this.log(`依赖管理失败: ${error.message}`, 'error');
            return false;
        }
    }

    // 2. 配置验证
    async validateConfiguration() {
        this.log('开始配置验证...', 'build');
        
        try {
            // 检查必需文件
            const requiredFiles = [
                '.env',
                'package.json',
                'src/app.js',
                'src/config/config.js',
                'src/routes/musicRoutes.js',
                'src/services/unlockService.js'
            ];
            
            for (const file of requiredFiles) {
                if (!fs.existsSync(file)) {
                    throw new Error(`缺少必需文件: ${file}`);
                }
                this.log(`✓ ${file}`, 'info');
            }
            
            // 验证配置文件
            require('dotenv').config();
            const config = require('../src/config/config');
            
            // 配置项检查
            const configChecks = [
                { name: '端口配置', check: () => config.server.port > 0 && config.server.port < 65536 },
                { name: '超时配置', check: () => config.performance.timeout >= 5000 },
                { name: '音源配置', check: () => Array.isArray(config.music.sources) && config.music.sources.length > 0 },
                { name: '日志配置', check: () => ['error', 'warn', 'info', 'debug'].includes(config.logging.level) },
                { name: '安全配置', check: () => config.security.rateLimitMaxRequests > 0 }
            ];
            
            for (const { name, check } of configChecks) {
                if (!check()) {
                    throw new Error(`配置验证失败: ${name}`);
                }
                this.log(`✓ ${name}`, 'info');
            }
            
            this.buildResults.configuration = true;
            this.log('配置验证完成', 'success');
            return true;
            
        } catch (error) {
            this.log(`配置验证失败: ${error.message}`, 'error');
            return false;
        }
    }

    // 3. 代码质量检查
    async checkCodeQuality() {
        this.log('开始代码质量检查...', 'build');
        
        try {
            // 运行测试套件
            this.log('执行测试套件...', 'info');
            const testResult = await this.runCommand('npm test');

            // 如果测试命令成功执行（退出码为0），认为测试通过
            this.log('测试套件执行成功', 'success');
            
            // 检查代码覆盖率
            const testOutput = testResult.stdout;
            const coverageMatch = testOutput.match(/All files.*?(\d+\.\d+)/);
            const coverage = coverageMatch ? parseFloat(coverageMatch[1]) : 0;
            
            if (coverage < 70) {
                this.log(`代码覆盖率较低: ${coverage}%`, 'warning');
            } else {
                this.log(`代码覆盖率: ${coverage}%`, 'success');
            }
            
            // 语法检查（如果有ESLint配置）
            if (fs.existsSync('.eslintrc.js') || fs.existsSync('.eslintrc.json')) {
                try {
                    await this.runCommand('npm run lint');
                    this.log('代码风格检查通过', 'success');
                } catch (error) {
                    this.log('代码风格检查发现问题', 'warning');
                }
            }
            
            this.buildResults.codeQuality = true;
            this.log('代码质量检查完成', 'success');
            return true;
            
        } catch (error) {
            this.log(`代码质量检查失败: ${error.message}`, 'error');
            return false;
        }
    }

    // 4. 生产环境优化
    async optimizeForProduction() {
        this.log('开始生产环境优化...', 'build');
        
        try {
            // 创建构建目录
            if (fs.existsSync(this.buildDir)) {
                if (process.platform === 'win32') {
                    await this.runCommand(`rmdir /s /q ${this.buildDir}`);
                } else {
                    await this.runCommand(`rm -rf ${this.buildDir}`);
                }
            }
            fs.mkdirSync(this.buildDir, { recursive: true });

            // 复制源代码
            this.log('复制源代码...', 'info');
            if (process.platform === 'win32') {
                await this.runCommand(`xcopy src ${this.buildDir}\\src /e /i /h`);
            } else {
                await this.runCommand(`cp -r src ${this.buildDir}/`);
            }
            
            // 复制必需文件
            const filesToCopy = [
                'package.json',
                'package-lock.json',
                '.env.example',
                'README.md',
                'RELEASE.md'
            ];
            
            for (const file of filesToCopy) {
                if (fs.existsSync(file)) {
                    if (process.platform === 'win32') {
                        await this.runCommand(`copy "${file}" "${this.buildDir}\\"`);
                    } else {
                        await this.runCommand(`cp ${file} ${this.buildDir}/`);
                    }
                    this.log(`✓ 复制 ${file}`, 'info');
                }
            }
            
            // 创建生产环境package.json
            this.createProductionPackageJson();
            
            // 生成启动脚本
            await this.createStartupScripts();
            
            // 创建Docker文件
            this.createDockerFiles();
            
            this.buildResults.optimization = true;
            this.log('生产环境优化完成', 'success');
            return true;
            
        } catch (error) {
            this.log(`生产环境优化失败: ${error.message}`, 'error');
            return false;
        }
    }

    // 创建生产环境package.json
    createProductionPackageJson() {
        const originalPackage = JSON.parse(fs.readFileSync('package.json', 'utf8'));
        
        const productionPackage = {
            name: originalPackage.name,
            version: originalPackage.version,
            description: originalPackage.description,
            main: originalPackage.main,
            scripts: {
                start: 'node src/app.js',
                'start:pm2': 'pm2 start ecosystem.config.js',
                'stop:pm2': 'pm2 stop ecosystem.config.js',
                'restart:pm2': 'pm2 restart ecosystem.config.js',
                'logs:pm2': 'pm2 logs'
            },
            dependencies: originalPackage.dependencies,
            keywords: originalPackage.keywords,
            author: originalPackage.author,
            license: originalPackage.license,
            engines: originalPackage.engines
        };
        
        fs.writeFileSync(
            path.join(this.buildDir, 'package.json'),
            JSON.stringify(productionPackage, null, 2)
        );
        
        this.log('✓ 生成生产环境package.json', 'info');
    }

    // 创建启动脚本
    async createStartupScripts() {
        // PM2 配置文件
        const pm2Config = {
            apps: [{
                name: 'music-unlock-server',
                script: 'src/app.js',
                instances: 'max',
                exec_mode: 'cluster',
                env: {
                    NODE_ENV: 'production',
                    PORT: 50090
                },
                error_file: 'logs/pm2-error.log',
                out_file: 'logs/pm2-out.log',
                log_file: 'logs/pm2-combined.log',
                time: true,
                max_memory_restart: '1G',
                node_args: '--max-old-space-size=1024'
            }]
        };
        
        fs.writeFileSync(
            path.join(this.buildDir, 'ecosystem.config.js'),
            `module.exports = ${JSON.stringify(pm2Config, null, 2)};`
        );
        
        // 启动脚本
        const startScript = `#!/bin/bash
# 音乐解锁服务启动脚本

echo "🎵 启动音乐解锁服务..."

# 检查Node.js版本
NODE_VERSION=$(node -v)
echo "Node.js版本: $NODE_VERSION"

# 检查环境变量
if [ ! -f ".env" ]; then
    echo "⚠️ 未找到.env文件，请先配置环境变量"
    exit 1
fi

# 安装依赖（如果需要）
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖..."
    npm install --production
fi

# 启动服务
echo "🚀 启动服务..."
npm start
`;
        
        fs.writeFileSync(path.join(this.buildDir, 'start.sh'), startScript);

        // 在Unix系统上设置执行权限
        if (process.platform !== 'win32') {
            try {
                await this.runCommand(`chmod +x ${this.buildDir}/start.sh`);
            } catch (error) {
                this.log('设置执行权限失败（可能是Windows环境）', 'warning');
            }
        }
        
        this.log('✓ 生成启动脚本', 'info');
    }

    // 创建Docker文件
    createDockerFiles() {
        const dockerfile = `# 🐳 UnblockNeteaseMusic Backend - Production Dockerfile
FROM node:18-alpine

# 设置工作目录
WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production && npm cache clean --force

# 复制源代码
COPY src/ ./src/

# 创建日志目录
RUN mkdir -p logs

# 设置用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001
RUN chown -R nodejs:nodejs /app
USER nodejs

# 暴露端口
EXPOSE 50090

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \\
    CMD node -e "require('http').get('http://localhost:50090/', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# 启动命令
CMD ["npm", "start"]
`;
        
        fs.writeFileSync(path.join(this.buildDir, 'Dockerfile'), dockerfile);
        
        // Docker Compose文件
        const dockerCompose = `version: '3.8'

services:
  music-unlock-server:
    build: .
    container_name: music-unlock-server
    restart: unless-stopped
    ports:
      - "\${PORT:-50090}:50090"
    environment:
      - NODE_ENV=production
    env_file:
      - .env
    volumes:
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:50090/', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
`;
        
        fs.writeFileSync(path.join(this.buildDir, 'docker-compose.yml'), dockerCompose);
        
        this.log('✓ 生成Docker文件', 'info');
    }

    // 5. 构建验证
    async validateBuild() {
        this.log('开始构建验证...', 'build');
        
        try {
            // 检查构建产物
            const requiredFiles = [
                'src/app.js',
                'package.json',
                'Dockerfile',
                'docker-compose.yml',
                'start.sh',
                'ecosystem.config.js'
            ];
            
            for (const file of requiredFiles) {
                const filePath = path.join(this.buildDir, file);
                if (!fs.existsSync(filePath)) {
                    throw new Error(`构建产物缺失: ${file}`);
                }
                this.log(`✓ ${file}`, 'info');
            }
            
            // 验证package.json
            const packageJson = JSON.parse(fs.readFileSync(path.join(this.buildDir, 'package.json'), 'utf8'));
            if (!packageJson.scripts.start) {
                throw new Error('package.json缺少start脚本');
            }
            
            this.buildResults.validation = true;
            this.log('构建验证完成', 'success');
            return true;
            
        } catch (error) {
            this.log(`构建验证失败: ${error.message}`, 'error');
            return false;
        }
    }

    // 生成构建报告
    generateBuildReport() {
        const duration = Date.now() - this.startTime;
        const durationMin = Math.round(duration / 1000 / 60 * 100) / 100;
        
        const report = `# 🔨 构建报告

## 📋 构建概览

**构建时间**: ${new Date().toISOString()}  
**构建耗时**: ${durationMin} 分钟  
**构建目录**: ${this.buildDir}  

## 📊 构建结果

| 阶段 | 状态 |
|------|------|
| 依赖管理 | ${this.buildResults.dependencies ? '✅ 成功' : '❌ 失败'} |
| 配置验证 | ${this.buildResults.configuration ? '✅ 成功' : '❌ 失败'} |
| 代码质量 | ${this.buildResults.codeQuality ? '✅ 成功' : '❌ 失败'} |
| 生产优化 | ${this.buildResults.optimization ? '✅ 成功' : '❌ 失败'} |
| 构建验证 | ${this.buildResults.validation ? '✅ 成功' : '❌ 失败'} |

## 🚀 部署说明

### 直接部署
\`\`\`bash
cd ${this.buildDir}
chmod +x start.sh
./start.sh
\`\`\`

### Docker部署
\`\`\`bash
cd ${this.buildDir}
docker-compose up -d
\`\`\`

### PM2部署
\`\`\`bash
cd ${this.buildDir}
npm run start:pm2
\`\`\`

---
**构建日志**: ${this.logFile}
`;
        
        const reportFile = `${this.buildDir}/BUILD_REPORT.md`;
        fs.writeFileSync(reportFile, report);
        this.log(`构建报告已生成: ${reportFile}`, 'success');
    }

    // 主执行函数
    async build() {
        this.log('开始构建流程...', 'build');
        
        const steps = [
            { name: '依赖管理', fn: () => this.manageDependencies() },
            { name: '配置验证', fn: () => this.validateConfiguration() },
            { name: '代码质量检查', fn: () => this.checkCodeQuality() },
            { name: '生产环境优化', fn: () => this.optimizeForProduction() },
            { name: '构建验证', fn: () => this.validateBuild() }
        ];
        
        for (const step of steps) {
            this.log(`执行: ${step.name}`, 'build');
            const success = await step.fn();
            
            if (!success) {
                this.log(`构建失败于: ${step.name}`, 'error');
                return false;
            }
        }
        
        // 生成构建报告
        this.generateBuildReport();
        
        const duration = Date.now() - this.startTime;
        this.log(`构建完成，耗时: ${Math.round(duration/1000)}秒`, 'success');
        
        return true;
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const builder = new BuildManager();
    builder.build().then(success => {
        process.exit(success ? 0 : 1);
    }).catch(error => {
        console.error('构建失败:', error);
        process.exit(1);
    });
}

module.exports = BuildManager;
