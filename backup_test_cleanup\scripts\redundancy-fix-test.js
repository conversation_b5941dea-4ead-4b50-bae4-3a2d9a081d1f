#!/usr/bin/env node

/**
 * 代码冗余修复测试脚本
 * 测试BATCH_CONCURRENCY修复和冗余常量清理的效果
 */

const { spawn } = require('child_process');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// 测试配置
const TEST_CONFIG = {
    server: {
        host: 'localhost',
        port: 50093,
        baseUrl: 'http://localhost:50093'
    },
    tests: {
        batchSongs: ['418602084', '347230', '186016', '123456', '789012'],
        concurrencyTest: 3, // 测试并发限制
        timeoutMs: 30000
    }
};

// 日志函数
function logHeader(message) {
    console.log('\n' + '='.repeat(60));
    console.log(`🧪 ${message}`);
    console.log('='.repeat(60));
}

function logInfo(message) {
    console.log(`[INFO] ${message}`);
}

function logSuccess(message) {
    console.log(`[SUCCESS] ✅ ${message}`);
}

function logError(message) {
    console.log(`[ERROR] ❌ ${message}`);
}

function logWarning(message) {
    console.log(`[WARNING] ⚠️ ${message}`);
}

class RedundancyFixTester {
    constructor() {
        this.serverProcess = null;
        this.testResults = {
            batchConcurrency: { tested: false, working: false, details: [] },
            constantsCleanup: { tested: false, working: false, details: [] },
            functionalityIntact: { tested: false, working: false, details: [] }
        };
    }

    // 创建测试环境配置
    createTestEnv() {
        const testEnvContent = `# 冗余修复测试环境
PORT=${TEST_CONFIG.server.port}
HOST=${TEST_CONFIG.server.host}
NODE_ENV=test

# 批量并发测试配置
BATCH_CONCURRENCY=${TEST_CONFIG.tests.concurrencyTest}
TIMEOUT=30000

# 频率限制配置
RATE_LIMIT_MAX_REQUESTS=50
RATE_LIMIT_WINDOW_MS=60000

# 音源配置
MUSIC_SOURCES=qq,migu,kuwo
FOLLOW_SOURCE_ORDER=true
ENABLE_FLAC=true
ENABLE_LOCAL_VIP=true
BLOCK_ADS=true
SELECT_MAX_BR=true

# 认证配置
NETEASE_COOKIE=
QQ_COOKIE=
MIGU_COOKIE=
JOOX_COOKIE=
YOUTUBE_KEY=
`;

        // 备份并创建测试环境
        if (fs.existsSync('.env')) {
            fs.copyFileSync('.env', '.env.backup');
        }
        fs.writeFileSync('.env', testEnvContent);
        logInfo('测试环境配置已创建');
    }

    // 启动测试服务器
    async startTestServer() {
        return new Promise((resolve, reject) => {
            logInfo('启动测试服务器...');
            
            this.serverProcess = spawn('node', ['src/app.js'], {
                stdio: ['pipe', 'pipe', 'pipe'],
                env: { ...process.env, NODE_ENV: 'test' }
            });

            let serverOutput = '';
            
            this.serverProcess.stdout.on('data', (data) => {
                const output = data.toString();
                serverOutput += output;
                
                if (output.includes('音乐解锁服务启动成功')) {
                    logSuccess(`测试服务器启动: ${TEST_CONFIG.server.baseUrl}`);
                    setTimeout(resolve, 2000);
                }
            });

            this.serverProcess.stderr.on('data', (data) => {
                const error = data.toString();
                if (!error.includes('DeprecationWarning')) {
                    console.error('服务器错误:', error);
                }
            });

            setTimeout(() => {
                if (!serverOutput.includes('音乐解锁服务启动成功')) {
                    reject(new Error('服务器启动超时'));
                }
            }, 15000);
        });
    }

    // 测试批量并发控制
    async testBatchConcurrency() {
        logHeader('测试批量并发控制功能');
        
        try {
            const startTime = Date.now();
            const testUrl = `${TEST_CONFIG.server.baseUrl}/music/unlock`;
            const songs = TEST_CONFIG.tests.batchSongs.join(',');
            
            logInfo(`测试批量解锁: ${TEST_CONFIG.tests.batchSongs.length}首歌曲`);
            logInfo(`配置的并发数: ${TEST_CONFIG.tests.concurrencyTest}`);
            
            const response = await axios.get(testUrl, {
                params: { songs },
                timeout: TEST_CONFIG.tests.timeoutMs
            });
            
            const duration = Date.now() - startTime;
            
            if (response.status === 200) {
                const data = response.data;
                
                logSuccess('批量解锁请求成功');
                logInfo(`处理时间: ${duration}ms`);
                logInfo(`解锁总数: ${data.解锁总数}`);
                logInfo(`解锁成功: ${data.解锁成功}`);
                logInfo(`解锁失败: ${data.解锁失败}`);
                
                this.testResults.batchConcurrency.tested = true;
                this.testResults.batchConcurrency.working = true;
                this.testResults.batchConcurrency.details = [
                    `处理${data.解锁总数}首歌曲`,
                    `成功${data.解锁成功}首，失败${data.解锁失败}首`,
                    `总耗时${duration}ms`,
                    `配置并发数: ${TEST_CONFIG.tests.concurrencyTest}`
                ];
                
                // 检查是否有并发控制的迹象
                const avgTimePerSong = duration / TEST_CONFIG.tests.batchSongs.length;
                logInfo(`平均每首歌处理时间: ${avgTimePerSong.toFixed(0)}ms`);
                
                if (duration > 1000) { // 如果总时间超过1秒，可能有并发控制
                    logSuccess('检测到可能的并发控制效果');
                } else {
                    logWarning('未明显检测到并发控制效果');
                }
                
            } else {
                logError(`批量解锁请求失败: ${response.status}`);
            }
            
        } catch (error) {
            logError(`批量并发测试失败: ${error.message}`);
            this.testResults.batchConcurrency.tested = true;
            this.testResults.batchConcurrency.working = false;
        }
    }

    // 测试常量清理效果
    async testConstantsCleanup() {
        logHeader('测试常量清理效果');
        
        try {
            // 检查constants.js文件内容
            const constantsPath = path.join(process.cwd(), 'src/utils/constants.js');
            const constantsContent = fs.readFileSync(constantsPath, 'utf8');
            
            // 检查是否还存在冗余常量
            const hasQualityLevels = constantsContent.includes('QUALITY_LEVELS');
            const hasQualityDisplayNames = constantsContent.includes('QUALITY_DISPLAY_NAMES');
            
            logInfo('检查常量文件内容...');
            
            if (hasQualityLevels) {
                logWarning('QUALITY_LEVELS 常量仍然存在');
            } else {
                logSuccess('QUALITY_LEVELS 常量已清理');
            }
            
            if (hasQualityDisplayNames) {
                logWarning('QUALITY_DISPLAY_NAMES 常量仍然存在');
            } else {
                logSuccess('QUALITY_DISPLAY_NAMES 常量已清理');
            }
            
            // 检查必要常量是否保留
            const hasHttpStatus = constantsContent.includes('HTTP_STATUS');
            const hasMusicSources = constantsContent.includes('MUSIC_SOURCES');
            const hasErrorCodes = constantsContent.includes('ERROR_CODES');
            
            logInfo('检查必要常量保留情况...');
            logInfo(`HTTP_STATUS: ${hasHttpStatus ? '✅ 保留' : '❌ 缺失'}`);
            logInfo(`MUSIC_SOURCES: ${hasMusicSources ? '✅ 保留' : '❌ 缺失'}`);
            logInfo(`ERROR_CODES: ${hasErrorCodes ? '✅ 保留' : '❌ 缺失'}`);
            
            const cleanupSuccess = !hasQualityLevels && !hasQualityDisplayNames;
            const essentialIntact = hasHttpStatus && hasMusicSources && hasErrorCodes;
            
            this.testResults.constantsCleanup.tested = true;
            this.testResults.constantsCleanup.working = cleanupSuccess && essentialIntact;
            this.testResults.constantsCleanup.details = [
                `QUALITY_LEVELS: ${hasQualityLevels ? '仍存在' : '已清理'}`,
                `QUALITY_DISPLAY_NAMES: ${hasQualityDisplayNames ? '仍存在' : '已清理'}`,
                `必要常量保留: ${essentialIntact ? '完整' : '缺失'}`
            ];
            
        } catch (error) {
            logError(`常量清理测试失败: ${error.message}`);
            this.testResults.constantsCleanup.tested = true;
            this.testResults.constantsCleanup.working = false;
        }
    }

    // 测试核心功能完整性
    async testFunctionalityIntact() {
        logHeader('测试核心功能完整性');
        
        try {
            // 测试音源管理API
            const sourceResponse = await axios.get(`${TEST_CONFIG.server.baseUrl}/music/source`);
            
            if (sourceResponse.status === 200) {
                logSuccess('音源管理API正常');
                
                const data = sourceResponse.data;
                logInfo(`音源总数: ${data.音源总数}`);
                logInfo(`使用的音源: ${JSON.stringify(data.使用的音源)}`);
            }
            
            // 测试单首歌曲解锁
            const unlockResponse = await axios.get(`${TEST_CONFIG.server.baseUrl}/music/unlock`, {
                params: { songs: '418602084' },
                timeout: 15000
            });
            
            if (unlockResponse.status === 200) {
                logSuccess('单首歌曲解锁API正常');
                
                const data = unlockResponse.data;
                logInfo(`解锁状态: ${data.消息}`);
                logInfo(`解锁成功率: ${data.解锁成功率}`);
            }
            
            this.testResults.functionalityIntact.tested = true;
            this.testResults.functionalityIntact.working = true;
            this.testResults.functionalityIntact.details = [
                '音源管理API正常',
                '音乐解锁API正常',
                '核心功能完整'
            ];
            
        } catch (error) {
            logError(`功能完整性测试失败: ${error.message}`);
            this.testResults.functionalityIntact.tested = true;
            this.testResults.functionalityIntact.working = false;
        }
    }

    // 停止服务器并清理
    cleanup() {
        if (this.serverProcess) {
            logInfo('停止测试服务器...');
            this.serverProcess.kill('SIGTERM');
            this.serverProcess = null;
        }
        
        // 恢复原环境配置
        if (fs.existsSync('.env.backup')) {
            fs.copyFileSync('.env.backup', '.env');
            fs.unlinkSync('.env.backup');
            logInfo('环境配置已恢复');
        }
    }

    // 生成测试报告
    generateReport() {
        logHeader('冗余修复测试报告');
        
        const report = `# 🧪 代码冗余修复测试报告

## 测试概述
- 测试时间: ${new Date().toISOString()}
- 测试服务器: ${TEST_CONFIG.server.baseUrl}
- 测试范围: 批量并发控制、常量清理、功能完整性

## 测试结果

### 1. 批量并发控制测试
- **测试状态**: ${this.testResults.batchConcurrency.tested ? '✅ 已测试' : '❌ 未测试'}
- **功能状态**: ${this.testResults.batchConcurrency.working ? '✅ 正常' : '❌ 异常'}
- **详细信息**:
${this.testResults.batchConcurrency.details.map(d => `  - ${d}`).join('\n')}

### 2. 常量清理测试
- **测试状态**: ${this.testResults.constantsCleanup.tested ? '✅ 已测试' : '❌ 未测试'}
- **清理状态**: ${this.testResults.constantsCleanup.working ? '✅ 成功' : '❌ 未完成'}
- **详细信息**:
${this.testResults.constantsCleanup.details.map(d => `  - ${d}`).join('\n')}

### 3. 功能完整性测试
- **测试状态**: ${this.testResults.functionalityIntact.tested ? '✅ 已测试' : '❌ 未测试'}
- **功能状态**: ${this.testResults.functionalityIntact.working ? '✅ 完整' : '❌ 受损'}
- **详细信息**:
${this.testResults.functionalityIntact.details.map(d => `  - ${d}`).join('\n')}

## 总体评估

${this.getOverallAssessment()}

---
*测试报告生成时间: ${new Date().toISOString()}*
`;

        console.log(report);
        
        const reportPath = `redundancy-fix-test-report-${Date.now()}.md`;
        fs.writeFileSync(reportPath, report);
        logSuccess(`测试报告已保存: ${reportPath}`);
    }

    getOverallAssessment() {
        const workingTests = Object.values(this.testResults).filter(r => r.working).length;
        const totalTests = Object.keys(this.testResults).length;
        
        if (workingTests === totalTests) {
            return '✅ **所有测试通过** - 冗余修复成功，功能完整性保持良好。';
        } else if (workingTests > 0) {
            return `⚠️ **部分测试通过** (${workingTests}/${totalTests}) - 需要进一步检查和修复。`;
        } else {
            return '❌ **测试失败** - 冗余修复可能存在问题，需要回滚和重新实施。';
        }
    }

    // 主测试流程
    async runTests() {
        try {
            logHeader('开始冗余修复测试');
            
            // 1. 准备测试环境
            this.createTestEnv();
            
            // 2. 启动测试服务器
            await this.startTestServer();
            
            // 3. 执行测试
            await this.testBatchConcurrency();
            await this.testConstantsCleanup();
            await this.testFunctionalityIntact();
            
            // 4. 生成报告
            this.generateReport();
            
        } catch (error) {
            logError(`测试过程失败: ${error.message}`);
        } finally {
            // 5. 清理
            this.cleanup();
            logInfo('测试完成，环境已清理');
        }
    }
}

// 主程序
async function main() {
    const tester = new RedundancyFixTester();
    await tester.runTests();
}

if (require.main === module) {
    main().catch(error => {
        console.error('测试失败:', error);
        process.exit(1);
    });
}

module.exports = RedundancyFixTester;
