#!/usr/bin/env node

/**
 * 启动测试脚本
 * 测试项目是否能正常启动和运行
 */

const { spawn } = require('child_process');
const axios = require('axios');
const fs = require('fs');

console.log('🚀 UnblockNeteaseMusic Backend 启动测试');
console.log('='.repeat(50));

// 检查环境
console.log('\n1. 环境检查:');
console.log(`   Node.js版本: ${process.version}`);
console.log(`   工作目录: ${process.cwd()}`);
console.log(`   .env文件: ${fs.existsSync('.env') ? '✅ 存在' : '❌ 缺失'}`);
console.log(`   package.json: ${fs.existsSync('package.json') ? '✅ 存在' : '❌ 缺失'}`);

// 检查关键文件
const keyFiles = [
    'src/app.js',
    'src/config/config.js',
    'src/routes/musicRoutes.js',
    'src/utils/constants.js'
];

console.log('\n2. 关键文件检查:');
keyFiles.forEach(file => {
    console.log(`   ${file}: ${fs.existsSync(file) ? '✅ 存在' : '❌ 缺失'}`);
});

// 检查依赖
console.log('\n3. 依赖检查:');
try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const dependencies = Object.keys(packageJson.dependencies || {});
    console.log(`   生产依赖: ${dependencies.length}个`);
    
    // 检查关键依赖
    const keyDeps = ['express', 'dotenv', '@unblockneteasemusic/server'];
    keyDeps.forEach(dep => {
        const exists = dependencies.includes(dep);
        console.log(`   ${dep}: ${exists ? '✅ 已安装' : '❌ 缺失'}`);
    });
} catch (error) {
    console.log(`   ❌ 读取package.json失败: ${error.message}`);
}

// 测试配置加载
console.log('\n4. 配置加载测试:');
try {
    require('dotenv').config();
    const config = require('../src/config/config');
    
    console.log(`   端口配置: ${config.server.port}`);
    console.log(`   环境配置: ${config.server.nodeEnv}`);
    console.log(`   音源配置: ${config.music.sources.length}个`);
    console.log('   ✅ 配置加载成功');
} catch (error) {
    console.log(`   ❌ 配置加载失败: ${error.message}`);
}

// 测试服务启动
console.log('\n5. 服务启动测试:');

async function testServerStartup() {
    return new Promise((resolve) => {
        const serverProcess = spawn('node', ['src/app.js'], {
            stdio: ['pipe', 'pipe', 'pipe'],
            env: { ...process.env, NODE_ENV: 'test' }
        });

        let output = '';
        let errorOutput = '';
        
        serverProcess.stdout.on('data', (data) => {
            const text = data.toString();
            output += text;
            console.log(`   [STDOUT] ${text.trim()}`);
            
            if (text.includes('音乐解锁服务启动成功')) {
                console.log('   ✅ 服务启动成功');
                setTimeout(() => {
                    serverProcess.kill('SIGTERM');
                    resolve({ success: true, output, error: errorOutput });
                }, 2000);
            }
        });

        serverProcess.stderr.on('data', (data) => {
            const text = data.toString();
            errorOutput += text;
            if (!text.includes('DeprecationWarning')) {
                console.log(`   [STDERR] ${text.trim()}`);
            }
        });

        serverProcess.on('error', (error) => {
            console.log(`   ❌ 启动失败: ${error.message}`);
            resolve({ success: false, output, error: error.message });
        });

        serverProcess.on('exit', (code) => {
            if (code !== 0 && !output.includes('音乐解锁服务启动成功')) {
                console.log(`   ❌ 进程异常退出，代码: ${code}`);
                resolve({ success: false, output, error: errorOutput });
            }
        });

        // 10秒超时
        setTimeout(() => {
            if (!output.includes('音乐解锁服务启动成功')) {
                console.log('   ⚠️ 启动超时，终止测试');
                serverProcess.kill('SIGTERM');
                resolve({ success: false, output, error: '启动超时' });
            }
        }, 10000);
    });
}

// 主测试流程
async function runStartupTest() {
    try {
        const result = await testServerStartup();
        
        console.log('\n6. 测试结果:');
        if (result.success) {
            console.log('   ✅ 项目启动测试通过');
            console.log('   ✅ 服务器可以正常启动和停止');
        } else {
            console.log('   ❌ 项目启动测试失败');
            if (result.error) {
                console.log(`   错误信息: ${result.error}`);
            }
        }
        
        // 生成测试报告
        const report = `# 🚀 项目启动测试报告

## 测试时间
${new Date().toISOString()}

## 测试结果
- **启动状态**: ${result.success ? '✅ 成功' : '❌ 失败'}
- **Node.js版本**: ${process.version}
- **工作目录**: ${process.cwd()}

## 输出日志
\`\`\`
${result.output}
\`\`\`

## 错误信息
\`\`\`
${result.error || '无错误'}
\`\`\`

## 结论
${result.success ? 
    '✅ 项目可以正常启动，所有基础功能正常。' : 
    '❌ 项目启动存在问题，需要检查配置和依赖。'}
`;
        
        const reportPath = `startup-test-report-${Date.now()}.md`;
        fs.writeFileSync(reportPath, report);
        console.log(`\n📄 测试报告已保存: ${reportPath}`);
        
    } catch (error) {
        console.log(`\n❌ 测试过程异常: ${error.message}`);
    }
}

// 运行测试
runStartupTest().then(() => {
    console.log('\n🏁 启动测试完成');
}).catch(error => {
    console.error('测试失败:', error);
    process.exit(1);
});
