/**
 * 应用常量定义模块
 * 定义系统中使用的所有常量和枚举值
 */

// HTTP状态码
const HTTP_STATUS = {
    OK: 200,
    CREATED: 201,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    METHOD_NOT_ALLOWED: 405,
    CONFLICT: 409,
    INTERNAL_SERVER_ERROR: 500,
    SERVICE_UNAVAILABLE: 503
};



// 支持的音源列表
const MUSIC_SOURCES = {
    QQ: 'qq',
    KUGOU: 'kugou',
    KUWO: 'kuwo',
    MIGU: 'migu',
    JOOX: 'joox',
    YOUTUBE: 'youtube',
    YTDLP: 'ytdlp',
    BILIBILI: 'bilibili',
    BILIVIDEO: 'bilivideo'
};

// 音源显示名称映射
const SOURCE_DISPLAY_NAMES = {
    [MUSIC_SOURCES.QQ]: 'QQ音乐',
    [MUSIC_SOURCES.KUGOU]: '酷狗音乐',
    [MUSIC_SOURCES.KUWO]: '酷我音乐',
    [MUSIC_SOURCES.MIGU]: '咪咕音乐',
    [MUSIC_SOURCES.JOOX]: 'JOOX',
    [MUSIC_SOURCES.YOUTUBE]: 'YouTube',
    [MUSIC_SOURCES.YTDLP]: 'YouTube (yt-dlp)',
    [MUSIC_SOURCES.BILIBILI]: 'B站音乐',
    [MUSIC_SOURCES.BILIVIDEO]: 'B站视频'
};



// 导入配置文件
const config = require('../config/config');

// API限制
const API_LIMITS = {
    REQUEST_TIMEOUT: config.performance.timeout    // 请求超时时间(毫秒)
    // MAX_BATCH_SIZE 已移除 - 无实际功能实现
};

// 错误代码
const ERROR_CODES = {
    VALIDATION_ERROR: 'VALIDATION_ERROR',
    SONG_NOT_FOUND: 'SONG_NOT_FOUND',
    SEARCH_FAILED: 'SEARCH_FAILED',
    UNLOCK_FAILED: 'UNLOCK_FAILED',
    SOURCE_UNAVAILABLE: 'SOURCE_UNAVAILABLE',
    RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
    INTERNAL_ERROR: 'INTERNAL_ERROR'
};

module.exports = {
    HTTP_STATUS,
    MUSIC_SOURCES,
    SOURCE_DISPLAY_NAMES,
    API_LIMITS,
    ERROR_CODES
};
