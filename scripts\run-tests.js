#!/usr/bin/env node

/**
 * UnblockNeteaseMusic Backend - 综合测试脚本
 * 执行完整的测试套件，包括单元测试、集成测试和E2E测试
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// 颜色输出工具
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function colorLog(color, message) {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function printHeader(title) {
    const line = '='.repeat(60);
    colorLog('cyan', line);
    colorLog('bright', `  ${title}`);
    colorLog('cyan', line);
}

function printSection(title) {
    colorLog('yellow', `\n📋 ${title}`);
    colorLog('yellow', '-'.repeat(40));
}

// 执行命令的Promise包装
function runCommand(command, args = [], options = {}) {
    return new Promise((resolve, reject) => {
        const child = spawn(command, args, {
            stdio: 'inherit',
            shell: true,
            ...options
        });

        child.on('close', (code) => {
            if (code === 0) {
                resolve(code);
            } else {
                reject(new Error(`Command failed with exit code ${code}`));
            }
        });

        child.on('error', (error) => {
            reject(error);
        });
    });
}

// 检查依赖
async function checkDependencies() {
    printSection('检查项目依赖');
    
    const packageJsonPath = path.join(process.cwd(), 'package.json');
    if (!fs.existsSync(packageJsonPath)) {
        throw new Error('package.json not found');
    }

    const nodeModulesPath = path.join(process.cwd(), 'node_modules');
    if (!fs.existsSync(nodeModulesPath)) {
        colorLog('yellow', '⚠️  node_modules not found, installing dependencies...');
        await runCommand('npm', ['install']);
    }

    colorLog('green', '✅ 依赖检查完成');
}

// 运行代码检查
async function runLinting() {
    printSection('代码质量检查');
    
    try {
        colorLog('blue', '🔍 运行 ESLint...');
        await runCommand('npm', ['run', 'lint']);
        colorLog('green', '✅ ESLint 检查通过');
    } catch (error) {
        colorLog('yellow', '⚠️  ESLint 检查发现问题，尝试自动修复...');
        try {
            await runCommand('npm', ['run', 'lint:fix']);
            colorLog('green', '✅ ESLint 自动修复完成');
        } catch (fixError) {
            colorLog('red', '❌ ESLint 自动修复失败');
            throw fixError;
        }
    }
}

// 运行单元测试
async function runUnitTests() {
    printSection('单元测试');
    
    colorLog('blue', '🧪 运行 Jest 单元测试...');
    await runCommand('npm', ['test']);
    colorLog('green', '✅ 单元测试完成');
}

// 运行覆盖率测试
async function runCoverageTests() {
    printSection('测试覆盖率分析');
    
    colorLog('blue', '📊 生成测试覆盖率报告...');
    await runCommand('npm', ['run', 'test:coverage']);
    colorLog('green', '✅ 覆盖率报告生成完成');
    
    // 检查覆盖率文件
    const coveragePath = path.join(process.cwd(), 'coverage', 'lcov-report', 'index.html');
    if (fs.existsSync(coveragePath)) {
        colorLog('cyan', `📈 覆盖率报告: file://${coveragePath}`);
    }
}

// 运行E2E测试
async function runE2ETests() {
    printSection('端到端测试');
    
    try {
        colorLog('blue', '🎭 运行 Playwright E2E 测试...');
        await runCommand('npm', ['run', 'test:e2e']);
        colorLog('green', '✅ E2E测试完成');
    } catch (error) {
        colorLog('yellow', '⚠️  E2E测试可能需要额外配置，跳过...');
    }
}

// 健康检查
async function runHealthCheck() {
    printSection('服务健康检查');
    
    try {
        colorLog('blue', '🏥 运行健康检查...');
        await runCommand('npm', ['run', 'health:check']);
        colorLog('green', '✅ 健康检查通过');
    } catch (error) {
        colorLog('yellow', '⚠️  健康检查脚本不存在，跳过...');
    }
}

// 生成测试报告
function generateTestReport() {
    printSection('测试报告总结');
    
    const reportData = {
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV || 'development',
        nodeVersion: process.version,
        platform: process.platform,
        testResults: {
            unit: '✅ 通过',
            coverage: '✅ 生成',
            e2e: '⚠️  可选',
            health: '⚠️  可选'
        }
    };

    const reportPath = path.join(process.cwd(), 'test-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(reportData, null, 2));
    
    colorLog('green', `📄 测试报告已保存: ${reportPath}`);
}

// 主函数
async function main() {
    const startTime = Date.now();
    
    printHeader('UnblockNeteaseMusic Backend - 综合测试套件');
    
    try {
        // 1. 检查依赖
        await checkDependencies();
        
        // 2. 代码质量检查
        await runLinting();
        
        // 3. 单元测试
        await runUnitTests();
        
        // 4. 覆盖率测试
        await runCoverageTests();
        
        // 5. E2E测试 (可选)
        await runE2ETests();
        
        // 6. 健康检查 (可选)
        await runHealthCheck();
        
        // 7. 生成报告
        generateTestReport();
        
        const duration = ((Date.now() - startTime) / 1000).toFixed(2);
        
        printHeader('测试完成');
        colorLog('green', `🎉 所有测试执行完成！`);
        colorLog('cyan', `⏱️  总耗时: ${duration}秒`);
        colorLog('cyan', `📊 查看详细报告: test-report.json`);
        
    } catch (error) {
        const duration = ((Date.now() - startTime) / 1000).toFixed(2);
        
        printHeader('测试失败');
        colorLog('red', `❌ 测试执行失败: ${error.message}`);
        colorLog('cyan', `⏱️  执行时间: ${duration}秒`);
        
        process.exit(1);
    }
}

// 处理命令行参数
const args = process.argv.slice(2);
if (args.includes('--help') || args.includes('-h')) {
    printHeader('使用说明');
    console.log(`
用法: node scripts/run-tests.js [选项]

选项:
  --help, -h     显示帮助信息
  --coverage     只运行覆盖率测试
  --unit         只运行单元测试
  --e2e          只运行E2E测试
  --lint         只运行代码检查

示例:
  node scripts/run-tests.js              # 运行完整测试套件
  node scripts/run-tests.js --unit       # 只运行单元测试
  node scripts/run-tests.js --coverage   # 只运行覆盖率测试
    `);
    process.exit(0);
}

// 根据参数执行特定测试
if (args.includes('--unit')) {
    runUnitTests().catch(console.error);
} else if (args.includes('--coverage')) {
    runCoverageTests().catch(console.error);
} else if (args.includes('--e2e')) {
    runE2ETests().catch(console.error);
} else if (args.includes('--lint')) {
    runLinting().catch(console.error);
} else {
    // 运行完整测试套件
    main().catch(console.error);
}
