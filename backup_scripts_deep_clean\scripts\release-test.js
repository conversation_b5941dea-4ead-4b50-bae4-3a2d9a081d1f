#!/usr/bin/env node

/**
 * 🧪 UnblockNeteaseMusic Backend - 发布前测试脚本
 * Release Testing Script
 * 
 * 功能：
 * - 完整的测试套件执行
 * - 配置验证
 * - 性能基准测试
 * - 安全检查
 * - 生成测试报告
 */

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');
const axios = require('axios');

class ReleaseTestRunner {
    constructor() {
        this.testResults = {
            unitTests: null,
            integrationTests: null,
            configValidation: null,
            securityCheck: null,
            performanceTest: null,
            apiTest: null
        };
        this.startTime = Date.now();
        this.reportFile = `test-reports/release-test-${new Date().toISOString().replace(/[:.]/g, '-')}.md`;
    }

    // 日志输出函数
    log(message, type = 'info') {
        const timestamp = new Date().toISOString();
        const prefix = {
            info: '📋',
            success: '✅',
            error: '❌',
            warning: '⚠️',
            test: '🧪'
        }[type] || '📋';
        
        console.log(`${prefix} [${timestamp}] ${message}`);
    }

    // 执行命令并返回结果
    async runCommand(command, options = {}) {
        return new Promise((resolve, reject) => {
            exec(command, { cwd: process.cwd(), ...options }, (error, stdout, stderr) => {
                if (error) {
                    reject({ error, stdout, stderr });
                } else {
                    resolve({ stdout, stderr });
                }
            });
        });
    }

    // 1. 单元测试
    async runUnitTests() {
        this.log('开始执行单元测试...', 'test');
        
        try {
            const result = await this.runCommand('npm test');
            
            // 解析测试结果
            const output = result.stdout;
            const testSuitesMatch = output.match(/Test Suites:.*?(\d+) passed/);
            const testsMatch = output.match(/Tests:.*?(\d+) passed/);
            const coverageMatch = output.match(/All files.*?(\d+\.\d+)/);
            
            this.testResults.unitTests = {
                status: 'passed',
                testSuites: testSuitesMatch ? parseInt(testSuitesMatch[1]) : 0,
                tests: testsMatch ? parseInt(testsMatch[1]) : 0,
                coverage: coverageMatch ? parseFloat(coverageMatch[1]) : 0,
                output: output
            };
            
            this.log(`单元测试完成: ${this.testResults.unitTests.tests}个测试通过`, 'success');
            return true;
            
        } catch (error) {
            this.testResults.unitTests = {
                status: 'failed',
                error: error.error?.message || 'Unknown error',
                output: error.stdout || ''
            };
            this.log(`单元测试失败: ${this.testResults.unitTests.error}`, 'error');
            return false;
        }
    }

    // 2. 配置验证
    async validateConfiguration() {
        this.log('开始配置验证...', 'test');
        
        try {
            // 检查必需文件
            const requiredFiles = ['.env', 'package.json', 'src/app.js', 'src/config/config.js'];
            const missingFiles = requiredFiles.filter(file => !fs.existsSync(file));
            
            if (missingFiles.length > 0) {
                throw new Error(`缺少必需文件: ${missingFiles.join(', ')}`);
            }
            
            // 验证环境变量
            require('dotenv').config();
            const config = require('../src/config/config');
            
            // 检查关键配置
            const configChecks = {
                port: config.server.port > 0 && config.server.port < 65536,
                timeout: config.performance.timeout >= 5000,
                sources: Array.isArray(config.music.sources) && config.music.sources.length > 0,
                logLevel: ['error', 'warn', 'info', 'debug'].includes(config.logging.level)
            };
            
            const failedChecks = Object.entries(configChecks)
                .filter(([key, passed]) => !passed)
                .map(([key]) => key);
            
            if (failedChecks.length > 0) {
                throw new Error(`配置验证失败: ${failedChecks.join(', ')}`);
            }
            
            this.testResults.configValidation = {
                status: 'passed',
                checks: Object.keys(configChecks).length,
                port: config.server.port,
                sources: config.music.sources.length
            };
            
            this.log('配置验证通过', 'success');
            return true;
            
        } catch (error) {
            this.testResults.configValidation = {
                status: 'failed',
                error: error.message
            };
            this.log(`配置验证失败: ${error.message}`, 'error');
            return false;
        }
    }

    // 3. 安全检查
    async runSecurityCheck() {
        this.log('开始安全检查...', 'test');
        
        try {
            const securityChecks = {
                envFile: this.checkEnvFileSecurity(),
                dependencies: await this.checkDependencySecurity(),
                codeSecrets: this.checkCodeSecrets()
            };
            
            const failedChecks = Object.entries(securityChecks)
                .filter(([key, result]) => !result.passed)
                .map(([key, result]) => `${key}: ${result.message}`);
            
            this.testResults.securityCheck = {
                status: failedChecks.length === 0 ? 'passed' : 'warning',
                checks: securityChecks,
                issues: failedChecks
            };
            
            if (failedChecks.length === 0) {
                this.log('安全检查通过', 'success');
            } else {
                this.log(`安全检查发现问题: ${failedChecks.length}个`, 'warning');
            }
            
            return true;
            
        } catch (error) {
            this.testResults.securityCheck = {
                status: 'failed',
                error: error.message
            };
            this.log(`安全检查失败: ${error.message}`, 'error');
            return false;
        }
    }

    // 检查.env文件安全性
    checkEnvFileSecurity() {
        try {
            const envContent = fs.readFileSync('.env', 'utf8');
            
            // 检查是否有明文密码或敏感信息
            const sensitivePatterns = [
                /password\s*=\s*[^#\s]/i,
                /secret\s*=\s*[^#\s]/i,
                /key\s*=\s*[a-zA-Z0-9]{20,}/i
            ];
            
            const foundSensitive = sensitivePatterns.some(pattern => pattern.test(envContent));
            
            return {
                passed: !foundSensitive,
                message: foundSensitive ? '发现可能的敏感信息' : '未发现明文敏感信息'
            };
            
        } catch (error) {
            return {
                passed: false,
                message: `无法读取.env文件: ${error.message}`
            };
        }
    }

    // 检查依赖安全性
    async checkDependencySecurity() {
        try {
            const result = await this.runCommand('npm audit --audit-level=high --json');
            const auditData = JSON.parse(result.stdout || '{}');
            
            const vulnerabilities = auditData.vulnerabilities || {};
            const highVulns = Object.values(vulnerabilities)
                .filter(vuln => ['high', 'critical'].includes(vuln.severity));
            
            return {
                passed: highVulns.length === 0,
                message: highVulns.length === 0 ? '未发现高危漏洞' : `发现${highVulns.length}个高危漏洞`
            };
            
        } catch (error) {
            return {
                passed: true,
                message: '依赖安全检查跳过（可能是网络问题）'
            };
        }
    }

    // 检查代码中的硬编码秘密
    checkCodeSecrets() {
        try {
            const srcFiles = this.getAllJsFiles('src');
            const secretPatterns = [
                /(['"`])(?:sk-|pk_|ghp_|gho_)[a-zA-Z0-9]{20,}\1/,
                /(['"`])[a-zA-Z0-9]{32,}\1/,
                /password\s*[:=]\s*['"`][^'"`]+['"`]/i
            ];
            
            for (const file of srcFiles) {
                const content = fs.readFileSync(file, 'utf8');
                const foundSecret = secretPatterns.some(pattern => pattern.test(content));
                
                if (foundSecret) {
                    return {
                        passed: false,
                        message: `在${file}中发现可能的硬编码秘密`
                    };
                }
            }
            
            return {
                passed: true,
                message: '未发现硬编码秘密'
            };
            
        } catch (error) {
            return {
                passed: false,
                message: `代码扫描失败: ${error.message}`
            };
        }
    }

    // 获取所有JS文件
    getAllJsFiles(dir) {
        const files = [];
        const items = fs.readdirSync(dir);
        
        for (const item of items) {
            const fullPath = path.join(dir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                files.push(...this.getAllJsFiles(fullPath));
            } else if (item.endsWith('.js')) {
                files.push(fullPath);
            }
        }
        
        return files;
    }

    // 4. API功能测试
    async runApiTest() {
        this.log('开始API功能测试...', 'test');
        
        try {
            // 启动服务器进行测试
            const config = require('../src/config/config');
            const baseUrl = `http://${config.server.host}:${config.server.port}`;
            
            // 等待服务器启动
            await this.waitForServer(baseUrl);
            
            // 测试API端点
            const apiTests = [
                { name: '服务状态', url: '/', expectedStatus: 200 },
                { name: '音源配置', url: '/music/source', expectedStatus: 200 },
                { name: '音乐解锁', url: '/music/unlock?songs=418602084', expectedStatus: 200 }
            ];
            
            const results = [];
            for (const test of apiTests) {
                try {
                    const response = await axios.get(`${baseUrl}${test.url}`, { timeout: 10000 });
                    results.push({
                        name: test.name,
                        status: 'passed',
                        statusCode: response.status,
                        responseTime: response.headers['x-response-time'] || 'N/A'
                    });
                } catch (error) {
                    results.push({
                        name: test.name,
                        status: 'failed',
                        error: error.message,
                        statusCode: error.response?.status || 'N/A'
                    });
                }
            }
            
            const passedTests = results.filter(r => r.status === 'passed').length;
            
            this.testResults.apiTest = {
                status: passedTests === apiTests.length ? 'passed' : 'failed',
                total: apiTests.length,
                passed: passedTests,
                results: results
            };
            
            this.log(`API测试完成: ${passedTests}/${apiTests.length}个通过`, 
                passedTests === apiTests.length ? 'success' : 'error');
            
            return passedTests === apiTests.length;
            
        } catch (error) {
            this.testResults.apiTest = {
                status: 'failed',
                error: error.message
            };
            this.log(`API测试失败: ${error.message}`, 'error');
            return false;
        }
    }

    // 等待服务器启动
    async waitForServer(baseUrl, maxAttempts = 10) {
        for (let i = 0; i < maxAttempts; i++) {
            try {
                await axios.get(baseUrl, { timeout: 2000 });
                return true;
            } catch (error) {
                if (i === maxAttempts - 1) {
                    throw new Error('服务器启动超时');
                }
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }
    }

    // 生成测试报告
    generateReport() {
        const duration = Date.now() - this.startTime;
        const report = this.createMarkdownReport(duration);
        
        // 确保报告目录存在
        const reportDir = path.dirname(this.reportFile);
        if (!fs.existsSync(reportDir)) {
            fs.mkdirSync(reportDir, { recursive: true });
        }
        
        fs.writeFileSync(this.reportFile, report);
        this.log(`测试报告已生成: ${this.reportFile}`, 'success');
    }

    // 创建Markdown报告
    createMarkdownReport(duration) {
        const timestamp = new Date().toISOString();
        const durationMin = Math.round(duration / 1000 / 60 * 100) / 100;
        
        return `# 🧪 发布前测试报告

## 📋 测试概览

**测试时间**: ${timestamp}  
**测试耗时**: ${durationMin} 分钟  
**测试环境**: ${process.env.NODE_ENV || 'development'}  

## 📊 测试结果汇总

${this.generateResultsSummary()}

## 📝 详细测试结果

${this.generateDetailedResults()}

## 🎯 测试结论

${this.generateConclusion()}

---
**报告生成时间**: ${timestamp}  
**测试脚本版本**: v2.0.0
`;
    }

    // 生成结果汇总
    generateResultsSummary() {
        const results = Object.entries(this.testResults);
        const passed = results.filter(([key, result]) => result?.status === 'passed').length;
        const total = results.filter(([key, result]) => result !== null).length;
        
        let summary = `| 测试项目 | 状态 | 详情 |\n|---------|------|------|\n`;
        
        for (const [key, result] of results) {
            if (result === null) continue;
            
            const status = result.status === 'passed' ? '✅ 通过' : 
                          result.status === 'warning' ? '⚠️ 警告' : '❌ 失败';
            const details = this.getResultDetails(key, result);
            
            summary += `| ${this.getTestName(key)} | ${status} | ${details} |\n`;
        }
        
        summary += `\n**总体通过率**: ${passed}/${total} (${Math.round(passed/total*100)}%)\n`;
        
        return summary;
    }

    // 获取测试名称
    getTestName(key) {
        const names = {
            unitTests: '单元测试',
            integrationTests: '集成测试',
            configValidation: '配置验证',
            securityCheck: '安全检查',
            performanceTest: '性能测试',
            apiTest: 'API测试'
        };
        return names[key] || key;
    }

    // 获取结果详情
    getResultDetails(key, result) {
        switch (key) {
            case 'unitTests':
                return result.status === 'passed' ? 
                    `${result.tests}个测试，${result.coverage}%覆盖率` : 
                    result.error;
            case 'configValidation':
                return result.status === 'passed' ? 
                    `${result.checks}项检查通过` : 
                    result.error;
            case 'securityCheck':
                return result.status === 'passed' ? 
                    '无安全问题' : 
                    `${result.issues?.length || 0}个问题`;
            case 'apiTest':
                return result.status === 'passed' ? 
                    `${result.passed}/${result.total}个API通过` : 
                    result.error;
            default:
                return result.status;
        }
    }

    // 生成详细结果
    generateDetailedResults() {
        let details = '';
        
        for (const [key, result] of Object.entries(this.testResults)) {
            if (result === null) continue;
            
            details += `### ${this.getTestName(key)}\n\n`;
            details += `**状态**: ${result.status}\n\n`;
            
            if (result.error) {
                details += `**错误**: ${result.error}\n\n`;
            }
            
            // 添加特定测试的详细信息
            if (key === 'unitTests' && result.status === 'passed') {
                details += `- 测试套件: ${result.testSuites}个\n`;
                details += `- 测试用例: ${result.tests}个\n`;
                details += `- 代码覆盖率: ${result.coverage}%\n\n`;
            }
            
            if (key === 'securityCheck' && result.issues) {
                details += `**发现的问题**:\n`;
                for (const issue of result.issues) {
                    details += `- ${issue}\n`;
                }
                details += '\n';
            }
        }
        
        return details;
    }

    // 生成结论
    generateConclusion() {
        const results = Object.values(this.testResults).filter(r => r !== null);
        const passed = results.filter(r => r.status === 'passed').length;
        const warnings = results.filter(r => r.status === 'warning').length;
        const failed = results.filter(r => r.status === 'failed').length;
        
        if (failed === 0 && warnings === 0) {
            return '🎉 **所有测试通过，项目已准备好发布！**';
        } else if (failed === 0) {
            return `⚠️ **测试基本通过，但有${warnings}个警告需要关注。**`;
        } else {
            return `❌ **发现${failed}个失败项，需要修复后才能发布。**`;
        }
    }

    // 主执行函数
    async run() {
        this.log('开始执行发布前测试...', 'info');
        
        // 执行所有测试
        await this.runUnitTests();
        await this.validateConfiguration();
        await this.runSecurityCheck();
        // await this.runApiTest(); // 需要服务器运行，暂时跳过
        
        // 生成报告
        this.generateReport();
        
        // 输出总结
        const results = Object.values(this.testResults).filter(r => r !== null);
        const passed = results.filter(r => r.status === 'passed').length;
        const total = results.length;
        
        this.log(`测试完成: ${passed}/${total}项通过`, passed === total ? 'success' : 'warning');
        
        return passed === total;
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const runner = new ReleaseTestRunner();
    runner.run().then(success => {
        process.exit(success ? 0 : 1);
    }).catch(error => {
        console.error('测试执行失败:', error);
        process.exit(1);
    });
}

module.exports = ReleaseTestRunner;
