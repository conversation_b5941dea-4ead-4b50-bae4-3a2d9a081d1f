#!/usr/bin/env node

/**
 * 代码冗余清理测试脚本
 * 验证清理冗余测试文件后的项目完整性
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

// 测试配置
const TEST_CONFIG = {
    redundantTestFiles: [
        'tests/coverage-boost.test.js',
        'tests/performance.test.js'
    ],
    backupDir: 'backup_tests',
    testCommand: 'npm test',
    timeout: 60000
};

// 日志函数
function logHeader(message) {
    console.log('\n' + '='.repeat(60));
    console.log(`🔍 ${message}`);
    console.log('='.repeat(60));
}

function logInfo(message) {
    console.log(`[INFO] ${message}`);
}

function logSuccess(message) {
    console.log(`[SUCCESS] ✅ ${message}`);
}

function logWarning(message) {
    console.log(`[WARNING] ⚠️ ${message}`);
}

function logError(message) {
    console.log(`[ERROR] ❌ ${message}`);
}

// 创建备份目录
function createBackup() {
    logInfo('创建测试文件备份...');
    
    if (!fs.existsSync(TEST_CONFIG.backupDir)) {
        fs.mkdirSync(TEST_CONFIG.backupDir, { recursive: true });
    }
    
    TEST_CONFIG.redundantTestFiles.forEach(filePath => {
        if (fs.existsSync(filePath)) {
            const fileName = path.basename(filePath);
            const backupPath = path.join(TEST_CONFIG.backupDir, fileName);
            fs.copyFileSync(filePath, backupPath);
            logInfo(`备份文件: ${filePath} -> ${backupPath}`);
        } else {
            logWarning(`文件不存在: ${filePath}`);
        }
    });
    
    logSuccess('测试文件备份完成');
}

// 分析冗余测试文件
function analyzeRedundantFiles() {
    logHeader('分析冗余测试文件');
    
    const analysis = {
        totalFiles: 0,
        totalLines: 0,
        fileDetails: []
    };
    
    TEST_CONFIG.redundantTestFiles.forEach(filePath => {
        if (fs.existsSync(filePath)) {
            const content = fs.readFileSync(filePath, 'utf8');
            const lines = content.split('\n').length;
            
            analysis.totalFiles++;
            analysis.totalLines += lines;
            analysis.fileDetails.push({
                path: filePath,
                lines: lines,
                size: fs.statSync(filePath).size
            });
            
            logInfo(`${filePath}: ${lines}行, ${fs.statSync(filePath).size}字节`);
        }
    });
    
    logInfo(`总计: ${analysis.totalFiles}个文件, ${analysis.totalLines}行代码`);
    return analysis;
}

// 运行测试套件
function runTests() {
    return new Promise((resolve, reject) => {
        logInfo('运行完整测试套件...');
        
        const testProcess = spawn('npm', ['test'], {
            stdio: 'pipe',
            shell: true
        });
        
        let stdout = '';
        let stderr = '';
        
        testProcess.stdout.on('data', (data) => {
            stdout += data.toString();
        });
        
        testProcess.stderr.on('data', (data) => {
            stderr += data.toString();
        });
        
        testProcess.on('close', (code) => {
            if (code === 0) {
                logSuccess('测试套件运行成功');
                resolve({ stdout, stderr, code });
            } else {
                logError(`测试套件运行失败，退出码: ${code}`);
                reject({ stdout, stderr, code });
            }
        });
        
        // 设置超时
        setTimeout(() => {
            testProcess.kill();
            reject(new Error('测试运行超时'));
        }, TEST_CONFIG.timeout);
    });
}

// 删除冗余文件
function removeRedundantFiles() {
    logHeader('删除冗余测试文件');
    
    const removedFiles = [];
    
    TEST_CONFIG.redundantTestFiles.forEach(filePath => {
        if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
            removedFiles.push(filePath);
            logSuccess(`删除文件: ${filePath}`);
        } else {
            logWarning(`文件不存在: ${filePath}`);
        }
    });
    
    return removedFiles;
}

// 恢复备份文件
function restoreBackup() {
    logInfo('恢复备份文件...');
    
    TEST_CONFIG.redundantTestFiles.forEach(filePath => {
        const fileName = path.basename(filePath);
        const backupPath = path.join(TEST_CONFIG.backupDir, fileName);
        
        if (fs.existsSync(backupPath)) {
            fs.copyFileSync(backupPath, filePath);
            logInfo(`恢复文件: ${backupPath} -> ${filePath}`);
        }
    });
    
    logSuccess('备份文件恢复完成');
}

// 清理备份目录
function cleanupBackup() {
    if (fs.existsSync(TEST_CONFIG.backupDir)) {
        fs.rmSync(TEST_CONFIG.backupDir, { recursive: true, force: true });
        logInfo('清理备份目录');
    }
}

// 生成清理报告
function generateReport(analysis, testResults, removedFiles) {
    const report = `# 🧹 代码冗余清理报告

## 清理概要
- **清理时间**: ${new Date().toISOString()}
- **清理文件数**: ${removedFiles.length}
- **减少代码行数**: ${analysis.totalLines}
- **测试状态**: ${testResults.code === 0 ? '✅ 通过' : '❌ 失败'}

## 清理详情

### 删除的文件
${removedFiles.map(file => `- ${file}`).join('\n')}

### 文件分析
${analysis.fileDetails.map(detail => 
    `- **${detail.path}**: ${detail.lines}行, ${detail.size}字节`
).join('\n')}

## 测试结果
\`\`\`
退出码: ${testResults.code}
\`\`\`

## 建议
${testResults.code === 0 ? 
    '✅ 清理成功，所有测试通过，可以安全提交更改。' : 
    '⚠️ 清理后测试失败，建议检查依赖关系。'
}

---
*报告生成时间: ${new Date().toLocaleString()}*
`;
    
    fs.writeFileSync('project_document/代码冗余清理报告.md', report);
    logSuccess('清理报告已生成: project_document/代码冗余清理报告.md');
}

// 主函数
async function main() {
    logHeader('UnblockNeteaseMusic 代码冗余清理测试');
    
    try {
        // 1. 分析冗余文件
        const analysis = analyzeRedundantFiles();
        
        // 2. 创建备份
        createBackup();
        
        // 3. 运行清理前测试
        logHeader('清理前测试验证');
        const beforeResults = await runTests();
        
        // 4. 删除冗余文件
        const removedFiles = removeRedundantFiles();
        
        // 5. 运行清理后测试
        logHeader('清理后测试验证');
        const afterResults = await runTests();
        
        // 6. 生成报告
        generateReport(analysis, afterResults, removedFiles);
        
        // 7. 清理备份（如果测试通过）
        if (afterResults.code === 0) {
            cleanupBackup();
            logSuccess('冗余清理完成，项目状态正常');
        } else {
            logWarning('测试失败，保留备份文件以便恢复');
        }
        
    } catch (error) {
        logError(`清理过程出错: ${error.message}`);
        
        // 恢复备份
        restoreBackup();
        cleanupBackup();
        
        process.exit(1);
    }
}

// 运行主函数
if (require.main === module) {
    main().catch(console.error);
}

module.exports = {
    createBackup,
    analyzeRedundantFiles,
    runTests,
    removeRedundantFiles,
    restoreBackup,
    generateReport
};
