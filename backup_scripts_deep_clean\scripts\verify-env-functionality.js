#!/usr/bin/env node

/**
 * 环境变量功能验证脚本
 * 验证RATE_LIMIT_WINDOW_MS、RATE_LIMIT_MAX_REQUESTS、MUSIC_SOURCES的实际运行时效果
 */

const axios = require('axios');
const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// 测试配置
const TEST_CONFIG = {
    server: {
        host: 'localhost',
        port: 50092, // 使用不同端口避免冲突
        baseUrl: 'http://localhost:50092'
    },
    tests: {
        rateLimitRequests: 10,  // 发送10个请求测试频率限制
        rateLimitInterval: 100, // 请求间隔100ms
        musicSources: ['qq', 'migu', 'kuwo'], // 测试音源配置
        testSongId: '418602084'
    }
};

// 日志函数
function logHeader(message) {
    console.log('\n' + '='.repeat(60));
    console.log(`🔍 ${message}`);
    console.log('='.repeat(60));
}

function logInfo(message) {
    console.log(`[INFO] ${message}`);
}

function logSuccess(message) {
    console.log(`[SUCCESS] ✅ ${message}`);
}

function logError(message) {
    console.log(`[ERROR] ❌ ${message}`);
}

function logWarning(message) {
    console.log(`[WARNING] ⚠️ ${message}`);
}

class EnvFunctionalityVerifier {
    constructor() {
        this.serverProcess = null;
        this.testResults = {
            rateLimitWindow: { tested: false, working: false, evidence: [] },
            rateLimitMax: { tested: false, working: false, evidence: [] },
            musicSources: { tested: false, working: false, evidence: [] }
        };
    }

    // 创建测试用的.env文件
    createTestEnvFile() {
        const testEnvContent = `# 测试环境变量功能验证
PORT=${TEST_CONFIG.server.port}
HOST=${TEST_CONFIG.server.host}
NODE_ENV=test

# 频率限制测试配置 - 设置较小值便于测试
RATE_LIMIT_MAX_REQUESTS=5
RATE_LIMIT_WINDOW_MS=10000

# 音源配置测试
MUSIC_SOURCES=${TEST_CONFIG.tests.musicSources.join(',')}

# 其他必需配置
TIMEOUT=30000
BATCH_CONCURRENCY=5
FOLLOW_SOURCE_ORDER=true
ENABLE_FLAC=true
ENABLE_LOCAL_VIP=true
BLOCK_ADS=true
SELECT_MAX_BR=true

# 认证配置（测试用空值）
NETEASE_COOKIE=
QQ_COOKIE=
MIGU_COOKIE=
JOOX_COOKIE=
YOUTUBE_KEY=
`;
        
        // 备份原.env文件
        if (fs.existsSync('.env')) {
            fs.copyFileSync('.env', '.env.backup');
            logInfo('已备份原.env文件为.env.backup');
        }
        
        // 创建测试.env文件
        fs.writeFileSync('.env', testEnvContent);
        logInfo('已创建测试.env文件');
    }

    // 启动测试服务器
    async startTestServer() {
        return new Promise((resolve, reject) => {
            logInfo('启动测试服务器...');
            
            this.serverProcess = spawn('node', ['src/app.js'], {
                stdio: ['pipe', 'pipe', 'pipe'],
                env: { ...process.env, NODE_ENV: 'test' }
            });

            let serverOutput = '';
            
            this.serverProcess.stdout.on('data', (data) => {
                const output = data.toString();
                serverOutput += output;
                
                if (output.includes('音乐解锁服务启动成功')) {
                    logSuccess(`测试服务器启动成功: ${TEST_CONFIG.server.baseUrl}`);
                    setTimeout(resolve, 1000); // 等待1秒确保服务器完全启动
                }
            });

            this.serverProcess.stderr.on('data', (data) => {
                console.error('服务器错误:', data.toString());
            });

            this.serverProcess.on('error', (error) => {
                logError(`启动服务器失败: ${error.message}`);
                reject(error);
            });

            // 10秒超时
            setTimeout(() => {
                if (!serverOutput.includes('音乐解锁服务启动成功')) {
                    reject(new Error('服务器启动超时'));
                }
            }, 10000);
        });
    }

    // 测试频率限制功能
    async testRateLimiting() {
        logHeader('测试频率限制功能');
        
        const testUrl = `${TEST_CONFIG.server.baseUrl}/music/source`;
        const requests = [];
        const results = [];

        logInfo(`发送${TEST_CONFIG.tests.rateLimitRequests}个请求测试频率限制...`);
        logInfo(`配置: MAX_REQUESTS=5, WINDOW=10秒`);

        // 快速发送多个请求
        for (let i = 0; i < TEST_CONFIG.tests.rateLimitRequests; i++) {
            const requestPromise = axios.get(testUrl, { 
                timeout: 5000,
                validateStatus: () => true // 接受所有状态码
            }).then(response => {
                return {
                    requestNumber: i + 1,
                    status: response.status,
                    rateLimited: response.status === 429,
                    timestamp: new Date().toISOString()
                };
            }).catch(error => {
                return {
                    requestNumber: i + 1,
                    status: error.response?.status || 'ERROR',
                    rateLimited: error.response?.status === 429,
                    error: error.message,
                    timestamp: new Date().toISOString()
                };
            });
            
            requests.push(requestPromise);
            
            // 短暂延迟避免请求过快
            if (i < TEST_CONFIG.tests.rateLimitRequests - 1) {
                await new Promise(resolve => setTimeout(resolve, TEST_CONFIG.tests.rateLimitInterval));
            }
        }

        // 等待所有请求完成
        const responses = await Promise.all(requests);
        
        // 分析结果
        const successfulRequests = responses.filter(r => r.status === 200);
        const rateLimitedRequests = responses.filter(r => r.rateLimited);
        
        logInfo(`成功请求: ${successfulRequests.length}`);
        logInfo(`被限制请求: ${rateLimitedRequests.length}`);
        
        // 验证频率限制是否生效
        if (rateLimitedRequests.length > 0) {
            logSuccess('频率限制功能正常工作！');
            this.testResults.rateLimitWindow.tested = true;
            this.testResults.rateLimitWindow.working = true;
            this.testResults.rateLimitMax.tested = true;
            this.testResults.rateLimitMax.working = true;
            
            this.testResults.rateLimitWindow.evidence = [
                `发送${TEST_CONFIG.tests.rateLimitRequests}个请求`,
                `${rateLimitedRequests.length}个请求被限制(状态码429)`,
                `限制配置: RATE_LIMIT_WINDOW_MS=10000, RATE_LIMIT_MAX_REQUESTS=5`,
                `实际效果: 超过5个请求后返回429状态码`
            ];
            
            this.testResults.rateLimitMax.evidence = this.testResults.rateLimitWindow.evidence;
        } else {
            logWarning('未检测到频率限制效果，可能需要更多请求或更短时间窗口');
            this.testResults.rateLimitWindow.tested = true;
            this.testResults.rateLimitWindow.working = false;
            this.testResults.rateLimitMax.tested = true;
            this.testResults.rateLimitMax.working = false;
        }

        // 详细输出每个请求的结果
        logInfo('详细请求结果:');
        responses.forEach(r => {
            const status = r.rateLimited ? `${r.status} (RATE LIMITED)` : r.status;
            logInfo(`  请求${r.requestNumber}: ${status} - ${r.timestamp}`);
        });
    }

    // 测试音源配置功能
    async testMusicSourcesConfig() {
        logHeader('测试音源配置功能');
        
        try {
            // 测试音源管理API
            const sourceResponse = await axios.get(`${TEST_CONFIG.server.baseUrl}/music/source`);
            
            if (sourceResponse.status === 200) {
                const data = sourceResponse.data;
                
                logInfo('音源管理API响应成功');
                logInfo(`配置的音源: ${TEST_CONFIG.tests.musicSources.join(', ')}`);
                
                // 检查响应中是否包含配置的音源
                if (data.使用的音源) {
                    const configuredSources = data.使用的音源;
                    const expectedSources = TEST_CONFIG.tests.musicSources;
                    
                    const sourcesMatch = expectedSources.every(source => 
                        configuredSources.includes(source)
                    );
                    
                    if (sourcesMatch) {
                        logSuccess('音源配置功能正常工作！');
                        this.testResults.musicSources.tested = true;
                        this.testResults.musicSources.working = true;
                        this.testResults.musicSources.evidence = [
                            `环境变量配置: MUSIC_SOURCES=${TEST_CONFIG.tests.musicSources.join(',')}`,
                            `API返回的音源: ${configuredSources.join(', ')}`,
                            `配置匹配: ${sourcesMatch ? '✅' : '❌'}`,
                            `实际效果: 音源管理API正确返回配置的音源列表`
                        ];
                    } else {
                        logWarning('音源配置不匹配');
                        logInfo(`期望: ${expectedSources.join(', ')}`);
                        logInfo(`实际: ${configuredSources.join(', ')}`);
                    }
                }
                
                // 显示完整响应信息
                logInfo('音源管理API完整响应:');
                console.log(JSON.stringify(data, null, 2));
                
            } else {
                logError(`音源管理API请求失败: ${sourceResponse.status}`);
            }
            
        } catch (error) {
            logError(`测试音源配置失败: ${error.message}`);
        }
    }

    // 停止测试服务器
    stopTestServer() {
        if (this.serverProcess) {
            logInfo('停止测试服务器...');
            this.serverProcess.kill('SIGTERM');
            this.serverProcess = null;
        }
    }

    // 恢复原.env文件
    restoreOriginalEnv() {
        if (fs.existsSync('.env.backup')) {
            fs.copyFileSync('.env.backup', '.env');
            fs.unlinkSync('.env.backup');
            logInfo('已恢复原.env文件');
        } else {
            logWarning('未找到备份的.env文件');
        }
    }

    // 生成测试报告
    generateReport() {
        logHeader('环境变量功能验证报告');
        
        const report = `# 🔍 环境变量功能验证报告

## 测试概述
- 测试时间: ${new Date().toISOString()}
- 测试服务器: ${TEST_CONFIG.server.baseUrl}
- 测试配置: RATE_LIMIT_MAX_REQUESTS=5, RATE_LIMIT_WINDOW_MS=10000

## 测试结果

### 1. RATE_LIMIT_WINDOW_MS
- **测试状态**: ${this.testResults.rateLimitWindow.tested ? '✅ 已测试' : '❌ 未测试'}
- **功能状态**: ${this.testResults.rateLimitWindow.working ? '✅ 正常工作' : '❌ 未生效'}
- **证据**:
${this.testResults.rateLimitWindow.evidence.map(e => `  - ${e}`).join('\n')}

### 2. RATE_LIMIT_MAX_REQUESTS  
- **测试状态**: ${this.testResults.rateLimitMax.tested ? '✅ 已测试' : '❌ 未测试'}
- **功能状态**: ${this.testResults.rateLimitMax.working ? '✅ 正常工作' : '❌ 未生效'}
- **证据**:
${this.testResults.rateLimitMax.evidence.map(e => `  - ${e}`).join('\n')}

### 3. MUSIC_SOURCES
- **测试状态**: ${this.testResults.musicSources.tested ? '✅ 已测试' : '❌ 未测试'}  
- **功能状态**: ${this.testResults.musicSources.working ? '✅ 正常工作' : '❌ 未生效'}
- **证据**:
${this.testResults.musicSources.evidence.map(e => `  - ${e}`).join('\n')}

## 结论

${this.getOverallConclusion()}

---
*报告生成时间: ${new Date().toISOString()}*
`;

        const reportPath = `env-functionality-verification-${Date.now()}.md`;
        fs.writeFileSync(reportPath, report);
        logSuccess(`验证报告已生成: ${reportPath}`);
        
        console.log('\n' + report);
    }

    getOverallConclusion() {
        const workingCount = Object.values(this.testResults).filter(r => r.working).length;
        const totalCount = Object.keys(this.testResults).length;
        
        if (workingCount === totalCount) {
            return '✅ **所有环境变量功能都正常工作**，配置从环境变量到实际运行时效果的完整链路验证通过。';
        } else if (workingCount > 0) {
            return `⚠️ **部分环境变量功能正常** (${workingCount}/${totalCount})，需要进一步检查未生效的配置。`;
        } else {
            return '❌ **环境变量功能验证失败**，需要检查配置和代码实现。';
        }
    }

    // 主测试流程
    async runVerification() {
        try {
            logHeader('开始环境变量功能验证');
            
            // 1. 创建测试环境
            this.createTestEnvFile();
            
            // 2. 启动测试服务器
            await this.startTestServer();
            
            // 3. 执行功能测试
            await this.testRateLimiting();
            await this.testMusicSourcesConfig();
            
            // 4. 生成报告
            this.generateReport();
            
        } catch (error) {
            logError(`验证过程失败: ${error.message}`);
        } finally {
            // 5. 清理
            this.stopTestServer();
            this.restoreOriginalEnv();
            logInfo('验证完成，环境已恢复');
        }
    }
}

// 主程序
async function main() {
    const verifier = new EnvFunctionalityVerifier();
    await verifier.runVerification();
}

// 处理程序退出
process.on('SIGINT', () => {
    console.log('\n收到中断信号，正在清理...');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n收到终止信号，正在清理...');
    process.exit(0);
});

if (require.main === module) {
    main().catch(error => {
        console.error('验证失败:', error);
        process.exit(1);
    });
}

module.exports = EnvFunctionalityVerifier;
