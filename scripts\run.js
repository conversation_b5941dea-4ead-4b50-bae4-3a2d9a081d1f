#!/usr/bin/env node

/**
 * 🚀 UnblockNeteaseMusic Backend - 运行脚本
 * Runtime Management Script
 * 
 * 功能：
 * - 环境检查和准备
 * - 服务启动和监控
 * - 健康检查
 * - 日志管理
 * - 优雅关闭
 */

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');
const http = require('http');

class RuntimeManager {
    constructor() {
        this.serverProcess = null;
        this.isShuttingDown = false;
        this.healthCheckInterval = null;
        this.logFile = `logs/runtime-${new Date().toISOString().replace(/[:.]/g, '-')}.log`;
        this.pidFile = 'runtime.pid';
        
        // 绑定信号处理
        this.setupSignalHandlers();
    }

    // 日志输出函数
    log(message, type = 'info') {
        const timestamp = new Date().toISOString();
        const prefix = {
            info: '📋',
            success: '✅',
            error: '❌',
            warning: '⚠️',
            runtime: '🚀',
            health: '💓'
        }[type] || '📋';
        
        const logMessage = `${prefix} [${timestamp}] ${message}`;
        console.log(logMessage);
        
        // 写入日志文件
        this.writeToLogFile(logMessage);
    }

    // 写入日志文件
    writeToLogFile(message) {
        try {
            const logDir = path.dirname(this.logFile);
            if (!fs.existsSync(logDir)) {
                fs.mkdirSync(logDir, { recursive: true });
            }
            fs.appendFileSync(this.logFile, message + '\n');
        } catch (error) {
            // 忽略日志写入错误
        }
    }

    // 执行命令并返回结果
    async runCommand(command, options = {}) {
        return new Promise((resolve, reject) => {
            exec(command, { cwd: process.cwd(), ...options }, (error, stdout, stderr) => {
                if (error) {
                    reject({ error, stdout, stderr });
                } else {
                    resolve({ stdout, stderr });
                }
            });
        });
    }

    // 1. 环境检查
    async checkEnvironment() {
        this.log('开始环境检查...', 'runtime');
        
        try {
            // 检查Node.js版本
            const nodeVersion = process.version;
            const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
            
            if (majorVersion < 16) {
                throw new Error(`Node.js版本过低: ${nodeVersion}，需要 >= 16.0.0`);
            }
            this.log(`Node.js版本: ${nodeVersion} ✓`, 'info');
            
            // 检查必需文件
            const requiredFiles = ['.env', 'package.json', 'src/app.js'];
            for (const file of requiredFiles) {
                if (!fs.existsSync(file)) {
                    throw new Error(`缺少必需文件: ${file}`);
                }
                this.log(`文件检查: ${file} ✓`, 'info');
            }
            
            // 检查依赖
            if (!fs.existsSync('node_modules')) {
                this.log('未找到node_modules，开始安装依赖...', 'warning');
                await this.runCommand('npm install');
                this.log('依赖安装完成', 'success');
            }
            
            // 加载和验证配置
            require('dotenv').config();
            const config = require('../src/config/config');
            
            this.log(`服务端口: ${config.server.port}`, 'info');
            this.log(`运行环境: ${config.server.env}`, 'info');
            this.log(`日志级别: ${config.logging.level}`, 'info');
            this.log(`音源配置: ${config.music.sources.join(', ')}`, 'info');
            
            // 检查端口是否被占用
            const isPortFree = await this.checkPort(config.server.port);
            if (!isPortFree) {
                throw new Error(`端口 ${config.server.port} 已被占用`);
            }
            this.log(`端口 ${config.server.port} 可用 ✓`, 'info');
            
            this.log('环境检查完成', 'success');
            return config;
            
        } catch (error) {
            this.log(`环境检查失败: ${error.message}`, 'error');
            throw error;
        }
    }

    // 检查端口是否可用
    async checkPort(port) {
        return new Promise((resolve) => {
            const server = http.createServer();
            
            server.listen(port, () => {
                server.close(() => {
                    resolve(true);
                });
            });
            
            server.on('error', () => {
                resolve(false);
            });
        });
    }

    // 2. 启动服务
    async startServer(config) {
        this.log('启动音乐解锁服务...', 'runtime');
        
        return new Promise((resolve, reject) => {
            // 启动子进程
            this.serverProcess = spawn('node', ['src/app.js'], {
                stdio: ['pipe', 'pipe', 'pipe'],
                env: { ...process.env }
            });
            
            // 写入PID文件
            fs.writeFileSync(this.pidFile, this.serverProcess.pid.toString());
            this.log(`服务进程ID: ${this.serverProcess.pid}`, 'info');
            
            // 处理输出
            this.serverProcess.stdout.on('data', (data) => {
                const output = data.toString().trim();
                if (output) {
                    this.log(`[SERVER] ${output}`, 'info');
                }
            });
            
            this.serverProcess.stderr.on('data', (data) => {
                const output = data.toString().trim();
                if (output) {
                    this.log(`[SERVER ERROR] ${output}`, 'error');
                }
            });
            
            // 处理进程退出
            this.serverProcess.on('exit', (code, signal) => {
                this.log(`服务进程退出: code=${code}, signal=${signal}`, code === 0 ? 'info' : 'error');
                this.cleanup();
                
                if (!this.isShuttingDown) {
                    this.log('服务意外退出，尝试重启...', 'warning');
                    setTimeout(() => {
                        this.restart();
                    }, 5000);
                }
            });
            
            this.serverProcess.on('error', (error) => {
                this.log(`服务启动失败: ${error.message}`, 'error');
                reject(error);
            });
            
            // 等待服务启动
            setTimeout(async () => {
                const isHealthy = await this.checkHealth(config);
                if (isHealthy) {
                    this.log('服务启动成功', 'success');
                    this.log(`服务地址: http://${config.server.host}:${config.server.port}`, 'info');
                    resolve();
                } else {
                    reject(new Error('服务启动后健康检查失败'));
                }
            }, 3000);
        });
    }

    // 3. 健康检查
    async checkHealth(config) {
        try {
            const url = `http://${config.server.host}:${config.server.port}/`;
            
            return new Promise((resolve) => {
                const req = http.get(url, { timeout: 5000 }, (res) => {
                    resolve(res.statusCode === 200);
                });
                
                req.on('error', () => {
                    resolve(false);
                });
                
                req.on('timeout', () => {
                    req.destroy();
                    resolve(false);
                });
            });
            
        } catch (error) {
            return false;
        }
    }

    // 4. 启动健康监控
    startHealthMonitoring(config) {
        this.log('启动健康监控...', 'health');
        
        this.healthCheckInterval = setInterval(async () => {
            const isHealthy = await this.checkHealth(config);
            
            if (!isHealthy) {
                this.log('健康检查失败，服务可能异常', 'error');
                
                // 检查进程是否还在运行
                if (this.serverProcess && !this.serverProcess.killed) {
                    this.log('进程仍在运行，可能是服务响应问题', 'warning');
                } else {
                    this.log('进程已退出，尝试重启服务', 'warning');
                    this.restart();
                }
            } else {
                this.log('健康检查通过', 'health');
            }
        }, 30000); // 每30秒检查一次
    }

    // 5. 重启服务
    async restart() {
        if (this.isShuttingDown) return;
        
        this.log('重启服务...', 'runtime');
        
        try {
            // 停止当前服务
            if (this.serverProcess && !this.serverProcess.killed) {
                this.serverProcess.kill('SIGTERM');
                
                // 等待进程退出
                await new Promise((resolve) => {
                    const timeout = setTimeout(() => {
                        this.serverProcess.kill('SIGKILL');
                        resolve();
                    }, 10000);
                    
                    this.serverProcess.on('exit', () => {
                        clearTimeout(timeout);
                        resolve();
                    });
                });
            }
            
            // 重新检查环境并启动
            const config = await this.checkEnvironment();
            await this.startServer(config);
            
            this.log('服务重启成功', 'success');
            
        } catch (error) {
            this.log(`服务重启失败: ${error.message}`, 'error');
        }
    }

    // 6. 优雅关闭
    async shutdown(signal = 'SIGTERM') {
        if (this.isShuttingDown) return;
        
        this.isShuttingDown = true;
        this.log(`收到${signal}信号，开始优雅关闭...`, 'runtime');
        
        // 停止健康监控
        if (this.healthCheckInterval) {
            clearInterval(this.healthCheckInterval);
            this.healthCheckInterval = null;
        }
        
        // 停止服务进程
        if (this.serverProcess && !this.serverProcess.killed) {
            this.log('发送关闭信号给服务进程...', 'info');
            this.serverProcess.kill('SIGTERM');
            
            // 等待进程优雅退出
            const exitPromise = new Promise((resolve) => {
                const timeout = setTimeout(() => {
                    this.log('强制关闭服务进程', 'warning');
                    this.serverProcess.kill('SIGKILL');
                    resolve();
                }, 10000);
                
                this.serverProcess.on('exit', () => {
                    clearTimeout(timeout);
                    resolve();
                });
            });
            
            await exitPromise;
        }
        
        // 清理资源
        this.cleanup();
        
        this.log('服务已优雅关闭', 'success');
        process.exit(0);
    }

    // 清理资源
    cleanup() {
        // 删除PID文件
        if (fs.existsSync(this.pidFile)) {
            fs.unlinkSync(this.pidFile);
        }
        
        // 清理进程引用
        this.serverProcess = null;
    }

    // 设置信号处理
    setupSignalHandlers() {
        // 优雅关闭信号
        process.on('SIGTERM', () => this.shutdown('SIGTERM'));
        process.on('SIGINT', () => this.shutdown('SIGINT'));
        
        // 处理未捕获的异常
        process.on('uncaughtException', (error) => {
            this.log(`未捕获的异常: ${error.message}`, 'error');
            this.log(error.stack, 'error');
            this.shutdown('EXCEPTION');
        });
        
        process.on('unhandledRejection', (reason, promise) => {
            this.log(`未处理的Promise拒绝: ${reason}`, 'error');
            this.shutdown('REJECTION');
        });
    }

    // 显示运行状态
    showStatus() {
        console.log(`
🎵 UnblockNeteaseMusic Backend Runtime Manager

📋 运行状态:
   进程ID: ${this.serverProcess?.pid || 'N/A'}
   PID文件: ${this.pidFile}
   日志文件: ${this.logFile}
   
🔧 控制命令:
   Ctrl+C: 优雅关闭服务
   
📊 监控信息:
   健康检查: 每30秒
   日志轮转: 按日期
   
🔗 相关链接:
   服务文档: http://localhost:50090/
   API接口: http://localhost:50090/music/unlock
   音源配置: http://localhost:50090/music/source
        `);
    }

    // 主运行函数
    async run() {
        try {
            this.log('UnblockNeteaseMusic Backend 运行管理器启动', 'runtime');
            
            // 环境检查
            const config = await this.checkEnvironment();
            
            // 启动服务
            await this.startServer(config);
            
            // 启动健康监控
            this.startHealthMonitoring(config);
            
            // 显示状态信息
            this.showStatus();
            
            this.log('运行管理器就绪，服务正在运行...', 'success');
            
        } catch (error) {
            this.log(`启动失败: ${error.message}`, 'error');
            process.exit(1);
        }
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const manager = new RuntimeManager();
    manager.run().catch(error => {
        console.error('运行失败:', error);
        process.exit(1);
    });
}

module.exports = RuntimeManager;
