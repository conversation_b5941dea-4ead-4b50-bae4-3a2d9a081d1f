/**
 * 模块初始化测试
 * 专门测试模块加载时的初始化代码
 */

describe('模块初始化测试', () => {
    let originalEnv;

    beforeEach(() => {
        // 备份原始环境变量
        originalEnv = { ...process.env };
        
        // 清理所有相关模块的缓存
        Object.keys(require.cache).forEach(key => {
            if (key.includes('unlockService') || key.includes('config')) {
                delete require.cache[key];
            }
        });
    });

    afterEach(() => {
        // 恢复原始环境变量
        process.env = originalEnv;
        
        // 清理全局变量
        delete global.proxy;
        delete global.hosts;
    });

    describe('UnlockService初始化', () => {
        test('模块正确加载', () => {
            // 重新加载模块以触发初始化
            const unlockService = require('../src/services/unlockService');

            // 验证模块加载成功
            expect(unlockService).toBeDefined();
            expect(unlockService.unlockSong).toBeDefined();
        });

        test('设置自定义Hosts环境变量', () => {
            // 设置hosts环境变量
            process.env.CUSTOM_HOSTS = '{"music.163.com": "127.0.0.1", "api.music.163.com": "***********"}';
            
            // 重新加载模块以触发初始化
            const unlockService = require('../src/services/unlockService');
            
            // 验证模块加载成功
            expect(unlockService).toBeDefined();
            expect(process.env.CUSTOM_HOSTS).toBe('{"music.163.com": "127.0.0.1", "api.music.163.com": "***********"}');
        });

        test('设置无效JSON格式的Hosts环境变量', () => {
            // 设置无效的JSON格式
            process.env.CUSTOM_HOSTS = 'invalid_json_format';
            
            // 重新加载模块以触发初始化和错误处理
            const unlockService = require('../src/services/unlockService');
            
            // 验证模块仍然加载成功（错误被捕获）
            expect(unlockService).toBeDefined();
            expect(process.env.CUSTOM_HOSTS).toBe('invalid_json_format');
        });

        test('设置音乐平台Cookie环境变量', () => {
            // 设置各种音乐平台的Cookie
            process.env.NETEASE_COOKIE = 'netease_test_cookie_value';
            process.env.QQ_COOKIE = 'qq_test_cookie_value';
            process.env.MIGU_COOKIE = 'migu_test_cookie_value';
            process.env.JOOX_COOKIE = 'joox_test_cookie_value';
            process.env.YOUTUBE_KEY = 'youtube_test_api_key';
            
            // 重新加载模块以触发初始化
            const unlockService = require('../src/services/unlockService');
            
            // 验证模块加载成功
            expect(unlockService).toBeDefined();
            
            // 验证环境变量被正确设置
            expect(process.env.NETEASE_COOKIE).toBe('netease_test_cookie_value');
            expect(process.env.QQ_COOKIE).toBe('qq_test_cookie_value');
            expect(process.env.MIGU_COOKIE).toBe('migu_test_cookie_value');
            expect(process.env.JOOX_COOKIE).toBe('joox_test_cookie_value');
            expect(process.env.YOUTUBE_KEY).toBe('youtube_test_api_key');
        });
    });

    describe('UnlockService初始化', () => {
        test('模块正常加载', () => {
            const unlockService = require('../src/services/unlockService');

            expect(unlockService).toBeDefined();
            expect(unlockService.unlockSong).toBeDefined();
            expect(unlockService.formatUnlockResult).toBeDefined();
        });

        test('测试模块加载和基本功能', async () => {
            // 简单测试模块的基本功能
            const unlockService = require('../src/services/unlockService');

            try {
                // 测试unlockSong的基本功能
                const result = await unlockService.unlockSong('123456');
                expect(result).toBeDefined();
            } catch (error) {
                // 如果出现错误，验证错误处理
                expect(error).toBeDefined();
            }
        });
    });


});
