#!/usr/bin/env node

/**
 * UnblockNeteaseMusic Backend - 自动化测试套件
 * Automated Test Suite for Music Unlock Service
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// 测试套件配置
const TEST_SUITE_CONFIG = {
    testTypes: [
        { name: 'lint', script: 'npm run lint', description: '代码质量检查', required: true },
        { name: 'unit', script: 'node scripts/unit-test-runner.js --coverage', description: '单元测试', required: true },
        { name: 'integration', script: 'npm run test:integration', description: '集成测试', required: false },
        { name: 'performance', script: 'node scripts/performance-test-runner.js', description: '性能测试', required: false },
        { name: 'e2e', script: 'node scripts/e2e-test-runner.js', description: 'E2E测试', required: false }
    ],
    reportDir: 'test-reports',
    timeout: 300000, // 5分钟总超时
    retries: 1
};

// 颜色定义
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

// 日志函数
function log(level, message) {
    const timestamp = new Date().toISOString();
    const color = colors[level] || colors.reset;
    console.log(`${color}[${level.toUpperCase()}]${colors.reset} ${timestamp} - ${message}`);
}

function logHeader(title) {
    console.log('\n' + colors.cyan + '='.repeat(70));
    console.log(`🤖 ${title}`);
    console.log('='.repeat(70) + colors.reset);
}

function logSuccess(message) {
    log('success', `✅ ${message}`);
}

function logError(message) {
    log('error', `❌ ${message}`);
}

function logInfo(message) {
    log('info', `ℹ️ ${message}`);
}

function logWarning(message) {
    log('warning', `⚠️ ${message}`);
}

// 自动化测试管理器
class AutomatedTestSuite {
    constructor() {
        this.testResults = [];
        this.startTime = null;
        this.endTime = null;
    }

    // 运行单个测试
    async runSingleTest(testConfig) {
        logInfo(`开始执行: ${testConfig.description}`);
        
        return new Promise((resolve) => {
            const startTime = Date.now();
            
            const testProcess = spawn('bash', ['-c', testConfig.script], {
                stdio: 'inherit',
                shell: true
            });
            
            const timeout = setTimeout(() => {
                testProcess.kill('SIGTERM');
                logError(`${testConfig.name} 测试超时`);
                resolve({
                    name: testConfig.name,
                    description: testConfig.description,
                    success: false,
                    duration: Date.now() - startTime,
                    error: 'Timeout',
                    required: testConfig.required
                });
            }, TEST_SUITE_CONFIG.timeout);
            
            testProcess.on('close', (code) => {
                clearTimeout(timeout);
                const duration = Date.now() - startTime;
                
                const result = {
                    name: testConfig.name,
                    description: testConfig.description,
                    success: code === 0,
                    exitCode: code,
                    duration: duration,
                    required: testConfig.required,
                    timestamp: new Date().toISOString()
                };
                
                if (result.success) {
                    logSuccess(`${testConfig.description} 完成 (${(duration / 1000).toFixed(2)}s)`);
                } else {
                    logError(`${testConfig.description} 失败 (退出码: ${code})`);
                }
                
                resolve(result);
            });
            
            testProcess.on('error', (error) => {
                clearTimeout(timeout);
                logError(`${testConfig.description} 执行错误: ${error.message}`);
                resolve({
                    name: testConfig.name,
                    description: testConfig.description,
                    success: false,
                    duration: Date.now() - startTime,
                    error: error.message,
                    required: testConfig.required
                });
            });
        });
    }

    // 运行所有测试
    async runAllTests(selectedTests = null) {
        logHeader('自动化测试套件开始执行');
        
        this.startTime = Date.now();
        
        const testsToRun = selectedTests || TEST_SUITE_CONFIG.testTypes;
        
        logInfo(`计划执行 ${testsToRun.length} 个测试套件`);
        
        for (const testConfig of testsToRun) {
            const result = await this.runSingleTest(testConfig);
            this.testResults.push(result);
            
            // 如果是必需的测试失败了，询问是否继续
            if (!result.success && result.required) {
                logWarning(`必需测试 ${testConfig.description} 失败`);
                
                // 在自动化模式下，继续执行其他测试
                logInfo('继续执行其他测试...');
            }
        }
        
        this.endTime = Date.now();
        
        return this.testResults;
    }

    // 分析测试结果
    analyzeResults() {
        logHeader('测试结果分析');
        
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(r => r.success).length;
        const failedTests = totalTests - passedTests;
        const requiredTests = this.testResults.filter(r => r.required);
        const requiredPassed = requiredTests.filter(r => r.success).length;
        const requiredFailed = requiredTests.length - requiredPassed;
        
        const totalDuration = this.endTime - this.startTime;
        const avgDuration = totalTests > 0 ? totalDuration / totalTests : 0;
        
        const analysis = {
            summary: {
                totalTests,
                passedTests,
                failedTests,
                requiredTests: requiredTests.length,
                requiredPassed,
                requiredFailed,
                successRate: totalTests > 0 ? (passedTests / totalTests * 100).toFixed(2) : 0,
                requiredSuccessRate: requiredTests.length > 0 ? (requiredPassed / requiredTests.length * 100).toFixed(2) : 0,
                totalDuration,
                avgDuration
            },
            details: this.testResults
        };
        
        // 输出分析结果
        logInfo(`总测试数: ${totalTests}`);
        logInfo(`通过测试: ${passedTests}`);
        logInfo(`失败测试: ${failedTests}`);
        logInfo(`成功率: ${analysis.summary.successRate}%`);
        logInfo(`必需测试成功率: ${analysis.summary.requiredSuccessRate}%`);
        logInfo(`总执行时间: ${(totalDuration / 1000).toFixed(2)}秒`);
        
        // 详细结果
        logInfo('\n详细测试结果:');
        for (const result of this.testResults) {
            const status = result.success ? '✅' : '❌';
            const required = result.required ? '[必需]' : '[可选]';
            const duration = (result.duration / 1000).toFixed(2);
            logInfo(`  ${status} ${required} ${result.description} (${duration}s)`);
            
            if (!result.success && result.error) {
                logInfo(`    错误: ${result.error}`);
            }
        }
        
        return analysis;
    }

    // 生成综合测试报告
    generateComprehensiveReport(analysis) {
        logHeader('生成综合测试报告');
        
        const timestamp = new Date().toISOString();
        const reportPath = `${TEST_SUITE_CONFIG.reportDir}/automated-test-report-${timestamp.replace(/[:.]/g, '-')}.md`;
        
        // 确保报告目录存在
        if (!fs.existsSync(TEST_SUITE_CONFIG.reportDir)) {
            fs.mkdirSync(TEST_SUITE_CONFIG.reportDir, { recursive: true });
        }
        
        const report = `# 🤖 自动化测试套件报告

## 📊 执行摘要
- **执行时间**: ${new Date().toLocaleString()}
- **测试环境**: Node.js ${process.version}
- **项目版本**: v1.0.0
- **测试套件版本**: v1.0

## 🎯 测试统计
- **总测试数**: ${analysis.summary.totalTests}
- **通过测试**: ${analysis.summary.passedTests}
- **失败测试**: ${analysis.summary.failedTests}
- **成功率**: ${analysis.summary.successRate}%
- **必需测试成功率**: ${analysis.summary.requiredSuccessRate}%
- **总执行时间**: ${(analysis.summary.totalDuration / 1000).toFixed(2)}秒

## 📈 详细测试结果

| 测试类型 | 描述 | 状态 | 必需 | 执行时间 | 错误信息 |
|---------|------|------|------|----------|----------|
${analysis.details.map(result => {
    const status = result.success ? '✅ 通过' : '❌ 失败';
    const required = result.required ? '是' : '否';
    const duration = (result.duration / 1000).toFixed(2) + 's';
    const error = result.error || '-';
    return `| ${result.name} | ${result.description} | ${status} | ${required} | ${duration} | ${error} |`;
}).join('\n')}

## 🔍 质量评估

### 代码质量
${this.getQualityAssessment('lint', analysis.details)}

### 功能测试
${this.getQualityAssessment('unit', analysis.details)}
${this.getQualityAssessment('integration', analysis.details)}

### 性能测试
${this.getQualityAssessment('performance', analysis.details)}

### 端到端测试
${this.getQualityAssessment('e2e', analysis.details)}

## 📝 总体评估
${this.getOverallAssessment(analysis)}

## 🔧 改进建议
${this.getImprovementSuggestions(analysis)}

## 📞 联系信息
- **测试执行**: 自动化测试套件
- **报告生成**: ${timestamp}
- **版本**: v1.0.0

---
*本报告由自动化测试套件生成*
`;
        
        fs.writeFileSync(reportPath, report);
        logSuccess(`综合测试报告已生成: ${reportPath}`);
        
        return reportPath;
    }

    // 获取质量评估
    getQualityAssessment(testType, results) {
        const result = results.find(r => r.name === testType);
        if (!result) {
            return '❓ 未执行';
        }
        return result.success ? '✅ 通过' : '❌ 失败';
    }

    // 获取总体评估
    getOverallAssessment(analysis) {
        const { requiredPassed, requiredTests, successRate } = analysis.summary;
        
        if (requiredPassed === requiredTests.length && successRate >= 80) {
            return '✅ **优秀** - 所有必需测试通过，整体质量优秀，可以部署到生产环境。';
        } else if (requiredPassed === requiredTests.length) {
            return '⚠️ **良好** - 必需测试通过，但部分可选测试失败，建议修复后部署。';
        } else {
            return '❌ **需要改进** - 必需测试失败，不建议部署，需要修复问题后重新测试。';
        }
    }

    // 获取改进建议
    getImprovementSuggestions(analysis) {
        const suggestions = [];
        
        for (const result of analysis.details) {
            if (!result.success) {
                switch (result.name) {
                    case 'lint':
                        suggestions.push('- 修复代码质量问题，运行 `npm run lint:fix` 自动修复');
                        break;
                    case 'unit':
                        suggestions.push('- 修复单元测试失败，检查测试用例和代码逻辑');
                        break;
                    case 'integration':
                        suggestions.push('- 检查集成测试环境，确保外部依赖可用');
                        break;
                    case 'performance':
                        suggestions.push('- 优化性能，检查响应时间和并发处理能力');
                        break;
                    case 'e2e':
                        suggestions.push('- 检查E2E测试环境，确保服务器正常运行');
                        break;
                }
            }
        }
        
        if (suggestions.length === 0) {
            return '✅ 无需改进，所有测试通过。';
        }
        
        return suggestions.join('\n');
    }
}

// 主函数
async function main() {
    try {
        logHeader('UnblockNeteaseMusic Backend - 自动化测试套件');
        
        // 解析命令行参数
        const args = process.argv.slice(2);
        let selectedTests = null;
        
        if (args.length > 0 && !args.includes('--all')) {
            selectedTests = TEST_SUITE_CONFIG.testTypes.filter(test => 
                args.includes(test.name)
            );
            
            if (selectedTests.length === 0) {
                logError('未找到指定的测试类型');
                logInfo('可用的测试类型: ' + TEST_SUITE_CONFIG.testTypes.map(t => t.name).join(', '));
                process.exit(1);
            }
        }
        
        // 创建测试套件
        const testSuite = new AutomatedTestSuite();
        
        // 运行测试
        await testSuite.runAllTests(selectedTests);
        
        // 分析结果
        const analysis = testSuite.analyzeResults();
        
        // 生成报告
        const reportPath = testSuite.generateComprehensiveReport(analysis);
        
        // 显示最终结果
        logHeader('自动化测试套件完成');
        logInfo(`测试报告: ${reportPath}`);
        
        const { requiredPassed, requiredTests, successRate } = analysis.summary;
        
        if (requiredPassed === requiredTests.length) {
            logSuccess('🎉 所有必需测试通过！');
            process.exit(0);
        } else {
            logError('❌ 必需测试失败，请修复问题');
            process.exit(1);
        }
        
    } catch (error) {
        logError(`自动化测试套件错误: ${error.message}`);
        process.exit(1);
    }
}

// 显示帮助信息
function showHelp() {
    console.log(`
🤖 UnblockNeteaseMusic Backend - 自动化测试套件

用法:
  node scripts/automated-test-suite.js [选项] [测试类型...]

选项:
  --all                运行所有测试 (默认)
  --help              显示帮助信息

测试类型:
  lint                代码质量检查
  unit                单元测试
  integration         集成测试
  performance         性能测试
  e2e                 E2E测试

示例:
  node scripts/automated-test-suite.js                    # 运行所有测试
  node scripts/automated-test-suite.js lint unit          # 只运行代码检查和单元测试
  node scripts/automated-test-suite.js --all              # 明确运行所有测试
`);
}

// 参数处理
if (process.argv.includes('--help')) {
    showHelp();
    process.exit(0);
}

// 运行主函数
if (require.main === module) {
    main();
}

module.exports = { AutomatedTestSuite };
