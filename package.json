{"name": "music-unlock-server", "version": "2.0.0", "description": "基于UnblockNeteaseMusic的音乐解锁服务后端系统", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["netease", "music", "unlock", "api", "nodejs", "express"], "author": "Music Unlock Server Team", "license": "MIT", "dependencies": {"@unblockneteasemusic/server": "^0.27.10", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "joi": "^17.11.0", "p-limit": "^3.1.0", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1"}, "devDependencies": {"@playwright/test": "^1.54.1", "axios": "^1.11.0", "eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.4"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}