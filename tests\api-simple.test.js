/**
 * 简化的API测试脚本
 * 使用Jest进行核心API接口测试
 */

const request = require('supertest');
const app = require('../src/app');

// 设置测试超时时间
jest.setTimeout(30000);

// 测试配置
const TEST_SONG_ID = '418602084'; // 周杰伦-稻香
const API_BASE = '/music';

describe('音乐解锁服务API测试', () => {
    let server;

    beforeAll(async () => {
        // 使用不同的端口进行测试
        process.env.PORT = '50093';
        process.env.NODE_ENV = 'test';
    });

    afterAll(async () => {
        if (server) {
            server.close();
        }
    });

    // 基础API测试
    describe('基础API', () => {
        test('GET / - 应该返回API文档', async () => {
            const response = await request(app)
                .get('/')
                .expect(200);

            expect(response.body.状态码).toBe(200);
            expect(response.body.消息).toBe('音乐解锁服务API文档');
            expect(response.body.版本).toBeDefined();
        });
    });

    // 音源管理测试
    describe('音源管理', () => {
        test('GET /music/source - 应该返回音源管理信息', async () => {
            const response = await request(app)
                .get(`${API_BASE}/source`)
                .expect(200);

            expect(response.body.状态码).toBe(200);
            expect(response.body.音源配置).toBeDefined();
            expect(response.body.已启用音源).toBeDefined();
        });
    });

    // 音乐解锁API测试
    describe('音乐解锁API', () => {
        test('GET /music/unlock?songs=:id - 应该返回单首歌曲解锁信息', async () => {
            const response = await request(app)
                .get(`${API_BASE}/unlock?songs=${TEST_SONG_ID}`)
                .expect(200);

            expect(response.body.状态码).toBe(200);
            expect(response.body.解锁总数).toBeDefined();
            expect(response.body.解锁成功).toBeDefined();
            expect(response.body.成功列表).toBeDefined();
        });
    });

    // 错误处理测试
    describe('错误处理', () => {
        test('GET /music/unlock - 缺少songs参数应返回400', async () => {
            const response = await request(app)
                .get(`${API_BASE}/unlock`)
                .expect(400);

            expect(response.body.状态码).toBe(400);
        });

        test('GET /music/nonexistent - 不存在的路径应返回404', async () => {
            const response = await request(app)
                .get(`${API_BASE}/nonexistent`)
                .expect(404);

            expect(response.body.状态码).toBe(404);
        });
    });

    // 性能测试
    describe('性能测试', () => {
        test('API响应时间应在合理范围内', async () => {
            const start = Date.now();
            await request(app)
                .get('/')
                .expect(200);
            const duration = Date.now() - start;

            expect(duration).toBeLessThan(1000); // 应在1秒内响应
        });
    });
});
