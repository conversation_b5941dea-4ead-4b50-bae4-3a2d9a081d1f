/**
 * 音乐解锁服务API测试脚本
 * 使用Jest进行API接口测试
 */

const request = require('supertest');
const app = require('../src/app');

// 测试配置 (硬编码优化 - testing配置已移除)
const TEST_SONG_ID = '418602084'; // 测试用歌曲ID
const TEST_SONG_IDS = ['418602084', '186016']; // 测试用歌曲ID列表
const API_BASE = '/music'; // API基础路径

describe('音乐解锁服务API测试', () => {
    
    // 服务状态测试（通过根路径）
    describe('服务状态', () => {
        test('GET / - 应该返回API文档和服务状态', async () => {
            const response = await request(app)
                .get('/')
                .expect(200);

            expect(response.body.状态码).toBe(200);
            expect(response.body.消息).toBe('音乐解锁服务API文档');
            expect(response.body.版本).toBeDefined();
        });
    });

    // 音源管理测试
    describe('音源管理', () => {
        test('GET /music/source - 应该返回音源管理信息', async () => {
            const response = await request(app)
                .get(`${API_BASE}/source`)
                .expect(200);

            expect(response.body.状态码).toBe(200);
            expect(response.body.音源配置).toBeDefined();
            expect(response.body.已启用音源).toBeDefined();
        });
    });

    // 音乐解锁API测试
    describe('音乐解锁API', () => {
        test('GET /music/unlock?songs=:id - 应该返回单首歌曲解锁信息', async () => {
            const response = await request(app)
                .get(`${API_BASE}/unlock?songs=${TEST_SONG_ID}`)
                .expect(200);

            expect(response.body.状态码).toBe(200);
            expect(response.body.解锁总数).toBeDefined();
            expect(response.body.解锁成功).toBeDefined();
            expect(response.body.成功列表).toBeDefined();
        });

        test('GET /music/unlock?songs=:id1,:id2 - 应该支持批量解锁', async () => {
            const response = await request(app)
                .get(`${API_BASE}/unlock?songs=${TEST_SONG_IDS.join(',')}`)
                .expect(200);

            expect(response.body.状态码).toBe(200);
            expect(response.body.解锁总数).toBe(TEST_SONG_IDS.length);
        });
    });

    // 参数验证测试
    describe('参数验证', () => {
        test('GET /music/unlock - 缺少songs参数应返回400', async () => {
            const response = await request(app)
                .get(`${API_BASE}/unlock`)
                .expect(400);

            expect(response.body.状态码).toBe(400);
            expect(response.body.消息).toContain('songs');
        });

        test('GET /music/unlock?songs=invalid - 无效歌曲ID应返回400', async () => {
            const response = await request(app)
                .get(`${API_BASE}/unlock?songs=invalid`)
                .expect(400);

            expect(response.body.状态码).toBe(400);
        });
    });

    // 错误处理测试
    describe('错误处理', () => {
        test('GET /music/nonexistent - 不存在的路径应返回404', async () => {
            const response = await request(app)
                .get(`${API_BASE}/nonexistent`)
                .expect(404);

            expect(response.body.状态码).toBe(404);
        });
    });

    // 性能测试
    describe('性能测试', () => {
        test('API响应时间应在合理范围内', async () => {
            const startTime = Date.now();

            await request(app)
                .get(`${API_BASE}/unlock?songs=${TEST_SONG_ID}`)
                .expect(200);

            const responseTime = Date.now() - startTime;
            expect(responseTime).toBeLessThan(5000); // 5秒内响应
        });

        test('并发请求处理', async () => {
            const promises = Array(5).fill().map(() =>
                request(app)
                    .get(`${API_BASE}/unlock?songs=${TEST_SONG_ID}`)
                    .expect(200)
            );

            const results = await Promise.all(promises);
            expect(results.length).toBe(5);
            results.forEach(result => {
                expect(result.body.状态码).toBe(200);
            });
        });
    });
});
