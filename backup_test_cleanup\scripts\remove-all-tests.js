#!/usr/bin/env node

/**
 * 删除所有测试文件脚本
 * ⚠️ 警告：此操作不可逆，将删除所有测试保护
 */

const fs = require('fs');
const path = require('path');

// 测试文件列表
const TEST_FILES = [
    'tests/api-simple.test.js',
    'tests/api-unit.test.js', 
    'tests/api.test.js',
    'tests/e2e-html.playwright.js',
    'tests/integration-fixed.test.js',
    'tests/module-init.test.js',
    'tests/services.test.js',
    'tests/utils.test.js'
];

// 需要修改的配置文件
const CONFIG_FILES = {
    'package.json': {
        removeScripts: ['test', 'test:coverage', 'test:watch'],
        removeDevDeps: ['jest', 'supertest', 'playwright']
    }
};

function logWarning(message) {
    console.log(`⚠️  [WARNING] ${message}`);
}

function logInfo(message) {
    console.log(`ℹ️  [INFO] ${message}`);
}

function logSuccess(message) {
    console.log(`✅ [SUCCESS] ${message}`);
}

function logError(message) {
    console.log(`❌ [ERROR] ${message}`);
}

// 显示警告信息
function showWarnings() {
    console.log('\n' + '='.repeat(60));
    console.log('🚨 删除所有测试文件 - 最终警告');
    console.log('='.repeat(60));
    
    logWarning('此操作将永久删除以下内容：');
    logWarning('• 所有测试文件（8个文件）');
    logWarning('• 测试相关的npm脚本');
    logWarning('• 代码质量保护机制');
    
    console.log('\n📉 删除后的影响：');
    logWarning('• 代码覆盖率从75%降至0%');
    logWarning('• 失去功能回归测试保护');
    logWarning('• 无法验证代码修改的安全性');
    logWarning('• CI/CD流程可能需要调整');
    
    console.log('\n🔄 此操作不可逆！');
    console.log('建议先备份项目或使用git提交当前状态\n');
}

// 创建备份
function createBackup() {
    const backupDir = 'backup_all_tests';
    
    if (!fs.existsSync(backupDir)) {
        fs.mkdirSync(backupDir, { recursive: true });
    }
    
    // 备份测试文件
    TEST_FILES.forEach(filePath => {
        if (fs.existsSync(filePath)) {
            const fileName = path.basename(filePath);
            const backupPath = path.join(backupDir, fileName);
            fs.copyFileSync(filePath, backupPath);
            logInfo(`备份: ${filePath} -> ${backupPath}`);
        }
    });
    
    // 备份package.json
    if (fs.existsSync('package.json')) {
        fs.copyFileSync('package.json', path.join(backupDir, 'package.json'));
        logInfo('备份: package.json');
    }
    
    logSuccess('备份创建完成');
}

// 删除测试文件
function removeTestFiles() {
    logInfo('开始删除测试文件...');
    
    let deletedCount = 0;
    
    TEST_FILES.forEach(filePath => {
        if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
            logSuccess(`删除: ${filePath}`);
            deletedCount++;
        } else {
            logInfo(`文件不存在: ${filePath}`);
        }
    });
    
    // 删除空的tests目录
    if (fs.existsSync('tests')) {
        const remainingFiles = fs.readdirSync('tests');
        if (remainingFiles.length === 0) {
            fs.rmdirSync('tests');
            logSuccess('删除空的tests目录');
        }
    }
    
    logSuccess(`共删除 ${deletedCount} 个测试文件`);
}

// 更新package.json
function updatePackageJson() {
    logInfo('更新package.json...');
    
    if (!fs.existsSync('package.json')) {
        logError('package.json不存在');
        return;
    }
    
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    
    // 删除测试脚本
    if (packageJson.scripts) {
        CONFIG_FILES['package.json'].removeScripts.forEach(script => {
            if (packageJson.scripts[script]) {
                delete packageJson.scripts[script];
                logInfo(`删除脚本: ${script}`);
            }
        });
    }
    
    // 可选：删除测试相关依赖
    logInfo('保留测试依赖（可手动删除）');
    
    fs.writeFileSync('package.json', JSON.stringify(packageJson, null, 2));
    logSuccess('package.json更新完成');
}

// 生成删除报告
function generateReport(deletedCount) {
    const report = `# 🗑️ 测试文件删除报告

## 删除概要
- **删除时间**: ${new Date().toISOString()}
- **删除文件数**: ${deletedCount}
- **操作状态**: ✅ 完成

## 删除的文件
${TEST_FILES.map(file => `- ${file}`).join('\n')}

## 修改的配置
- **package.json**: 删除测试相关脚本

## ⚠️ 重要提醒
- 项目已失去所有测试保护
- 代码覆盖率从75%降至0%
- 建议在重要修改前手动测试功能
- 备份文件保存在 backup_all_tests/ 目录

## 恢复方法
如需恢复测试文件：
\`\`\`bash
cp backup_all_tests/* tests/
cp backup_all_tests/package.json .
npm install
\`\`\`

---
*删除时间: ${new Date().toLocaleString()}*
`;
    
    fs.writeFileSync('project_document/测试文件删除报告.md', report);
    logSuccess('删除报告已生成');
}

// 主函数
async function main() {
    showWarnings();
    
    // 等待用户确认
    const readline = require('readline').createInterface({
        input: process.stdin,
        output: process.stdout
    });
    
    const answer = await new Promise(resolve => {
        readline.question('确认删除所有测试文件？(输入 "DELETE ALL TESTS" 确认): ', resolve);
    });
    
    readline.close();
    
    if (answer !== 'DELETE ALL TESTS') {
        logInfo('操作已取消');
        return;
    }
    
    try {
        // 1. 创建备份
        createBackup();
        
        // 2. 删除测试文件
        removeTestFiles();
        
        // 3. 更新配置
        updatePackageJson();
        
        // 4. 生成报告
        generateReport(TEST_FILES.length);
        
        console.log('\n' + '='.repeat(60));
        logSuccess('所有测试文件已删除');
        logWarning('项目已失去测试保护，请谨慎修改代码');
        console.log('='.repeat(60));
        
    } catch (error) {
        logError(`删除过程出错: ${error.message}`);
        logInfo('可从backup_all_tests/目录恢复文件');
    }
}

// 运行
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { removeTestFiles, updatePackageJson, createBackup };
